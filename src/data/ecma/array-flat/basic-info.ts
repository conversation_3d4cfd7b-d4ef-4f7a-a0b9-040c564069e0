/**
 * 📋 Tab 1: 基本信息 (basic-info.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * 🛡️ 数据一致性强约束（零容忍错误）
 * ⚠️ 生成基本信息后必须同时更新 index.ts 文件，确保数据一致性
 *
 * 🚨 强制检查清单:
 * - [ ] basic-info.ts 已更新实际内容
 * - [ ] index.ts 的 description 字段已从 basicInfo.definition 同步
 * - [ ] index.ts 的 syntax 字段已从 basicInfo.syntax 同步
 * - [ ] index.ts 的 example 字段已从 basicInfo.commonUseCases[0].code 同步
 * - [ ] index.ts 的 notes 字段已从 basicInfo.limitations[0] 同步
 * - [ ] index.ts 的 version 和 tags 字段已根据API特性设置
 * - [ ] 页面不再显示 \\{XXX\\} 格式的骨架内容
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🎯 Tab定位与价值 - 升级版
 *
 * 🎭 **身份定位**：你是一位精通数据处理的工程师，经常需要清理和转换来自不同来源的复杂数据结构。
 *
 * 💡 **核心使命**：基本信息Tab是`Array.flat()`的**功能说明书**，帮助开发者在3分钟内理解：
 * - 🎯 **核心功能**：它是如何将一个令人头疼的多维数组瞬间"拍平"的。
 * - 🕹️ **控制旋钮**：`depth`参数如何像一个精确的调节器，控制"拍平"的力度。
 * - 🚧 **特殊路况**：它是如何优雅地处理数组中的"空洞"（empty slots）的。
 *
 * 🌊 **表达温度**：像一位经验丰富的同事，拍着你的肩膀说：
 * "别再手动写递归了，用 `flat()` 吧，一行代码就搞定，既安全又高效。省下来的时间，喝杯咖啡不好吗？"
 *
 * 🔍 **知识考古精神**：不仅要知道`flat()`怎么用，更要理解"为什么需要它"。
 * 探索在没有`flat()`的年代，开发者们是如何与嵌套数组作斗争的，从而体会这个API带来的巨大便利。
 *
 * 📊 内容结构要求 - 必须包含的11个核心部分：
 * - definition: 一句话定义（30-50字）- 战略定位+核心使命+存在价值+技术优势
 * - introduction: 详细介绍（100-200字）- 历史背景+设计哲学+核心功能+生态地位+技术优势
 * - syntax: 完整TypeScript语法 - 🆕 ECMAScript规范定位+TypeScript类型定义分析+源码定位+泛型解析+重载分析
 * - quickExample: 完整基础示例 - 完整可运行+中文注释+基础用法+简洁明了
 * - scenarioDiagram: 业务场景图表 - 多图表支持+业务场景+相关技术+技术特性+中文标注
 * - parameters: 详细参数说明 - 设计原理+类型约束+性能影响+最佳实践
 * - returnValue: 返回值说明 - 类型+描述+属性+示例
 * - keyFeatures: 核心特性 - 3-5个技术亮点+差异化优势+独特能力+importance等级
 * - limitations: 使用限制 - 3-5条限制+技术原因+替代方案
 * - commonUseCases: 🆕 常见用例 - 多个难度层次+完整代码示例+中文解释+Mermaid图表
 * - bestPractices: 最佳实践 - 5-8条实用建议+官方推荐+社区实践+性能优化
 * - warnings: 重要警告 - 3-5个常见错误+安全风险+性能陷阱+兼容性问题
 */

import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: `Array.prototype.flat() 是 ES2019 引入的数组实例方法，用于将嵌套的数组结构按照指定的深度递归展开，返回一个新的扁平化后的一维数组。它极大地简化了多维数组的处理，是现代数据处理和转换中不可或缺的工具。`,

  introduction: `在 Array.prototype.flat() 出现之前，JavaScript 开发者需要依赖递归、循环或库函数（如 Lodash 的 _.flatten）来处理嵌套数组的扁平化。这些方法不仅代码冗长，而且容易出错，特别是在处理深层嵌套或空槽时。ES2019 正式引入 flat() 方法，为数组扁平化提供了原生、高效且易于理解的解决方案。flat() 的设计哲学是提供一个声明式、可控的工具来解决这个常见问题。它允许开发者通过 depth 参数精确控制扁平化的深度，甚至可以处理无限深度的嵌套。这个API的出现，不仅提升了代码的简洁性和可读性，也反映了JavaScript语言向更高级、更便捷的数据操作能力演进的趋势。`,

  syntax: `
// 🆕 ECMAScript规范定位信息
/**
 * 规范定义位置：
 * - ECMAScript 2019 (ES10) 规范：********* Array.prototype.flat ( [ depth ] )
 * - MDN文档：https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/flat
 */

// === 🆕 TypeScript类型定义分析 ===
/**
 * Array.prototype.flat() 类型定义位置：
 * - 核心类型：lib.es2019.array.d.ts
 */

interface Array<T> {
  /**
   * 返回一个新数组，所有子数组元素递归地连接到其中，直到指定的深度。
   * @param depth 递归的最大深度。默认为1。
   */
  flat<A, D extends number = 1>(
    this: A,
    depth?: D
  ): FlatArray<A, D>[];
}

// === 基础语法形式 ===

// 1. 不带参数（默认深度为 1）
const arr1 = [1, 2, [3, 4]];
arr1.flat();
// 输出: [1, 2, 3, 4]

// 2. 指定深度为 2
const arr2 = [1, [2, [3, [4]]]];
arr2.flat(2);
// 输出: [1, 2, 3, [4]]

// 3. 使用 Infinity 实现完全扁平化
const arr3 = [1, [2, [3, [4]]]];
arr3.flat(Infinity);
// 输出: [1, 2, 3, 4]

// 4. 处理数组中的空槽 (empty slots)
const arr4 = [1, 2, , 4, 5];
arr4.flat();
// 输出: [1, 2, 4, 5] (空槽被移除)

// 5. flat() 不会改变原数组
const originalArray = [1, [2, 3]];
const flattenedArray = originalArray.flat();
console.log(flattenedArray); // [1, 2, 3]
console.log(originalArray);  // [1, [2, 3]] (原数组保持不变)
  `,

  quickExampleCode: `
// 场景：假设我们从多个API端点获取了用户标签数据，数据结构是一个嵌套数组。
// 我们需要将这些标签扁平化，并移除重复项，以便在UI上显示。

const userTagsFromApiA = ['React', 'TypeScript'];
const userTagsFromApiB = [['Node.js', 'Express'], ['MongoDB']];
const userTagsFromApiC = ['CSS', ['Sass', 'Less']];

const allTagsNested = [userTagsFromApiA, userTagsFromApiB, userTagsFromApiC];
// 原始数据: 
// [
//   ["React", "TypeScript"],
//   [["Node.js", "Express"], ["MongoDB"]],
//   ["CSS", ["Sass", "Less"]]
// ]

// 使用 flat(Infinity) 进行完全扁平化
console.log('--- 步骤1: 完全扁平化 ---');
const flattenedTags = allTagsNested.flat(Infinity);
console.log(flattenedTags);
// 输出: ["React", "TypeScript", "Node.js", "Express", "MongoDB", "CSS", "Sass", "Less"]

// 使用 Set 数据结构移除重复项
console.log('--- 步骤2: 移除重复标签 ---');
const uniqueTags = [...new Set(flattenedTags)];
console.log(uniqueTags);
// 输出: (如果无重复，则与上一步相同)

// 最终排序用于显示
console.log('--- 步骤3: 排序以便显示 ---');
const sortedTags = uniqueTags.sort();
console.log(sortedTags);
// 输出: ["CSS", "Express", "Less", "MongoDB", "Node.js", "React", "Sass", "TypeScript"]
  `,

  quickExampleDiagram: `
\`\`\`mermaid
graph TD
    A[开始: 嵌套的标签数组] --> B{flat(Infinity)};
    B --> C[生成扁平化数组];
    C --> D{new Set(...)};
    D --> E[生成唯一的标签集合];
    E --> F{sort()};
    F --> G[生成排序后的最终数组];
    G --> H[结束: 可供UI使用的标签列表];

    subgraph OriginalData ["原始数据"]
        direction LR
        A1[React, TS]
        A2["[Node, Exp], [Mongo]"]
        A3["CSS, [Sass, Less]"]
    end

    subgraph FlattenedData ["扁平化后"]
        C1[React, TS, Node, Exp, Mongo, CSS, Sass, Less]
    end
    
    subgraph FinalResult ["最终结果"]
        G1[CSS, Express, Less, ...]
    end

    style A fill:#e1f5fe,stroke:#01579b
    style H fill:#e8f5e8,stroke:#1b5e20
\`\`\`
  `,

  parameters: [
    {
      name: "depth",
      type: "number",
      required: false,
      description: "指定扁平化的深度。默认为 1。",
      details: "可以是任何非负整数。如果为0，则不进行扁平化。如果为 Infinity，则会递归到最深层，实现完全扁平化。如果为负数，则视为0。",
    },
  ],

  returnValue: {
    type: "Array",
    description: "返回一个包含原数组所有元素和子数组元素的新数组，不会修改原数组。",
  },

  keyFeatures: [
    {
      feature: "声明式扁平化",
      description: "提供了一种清晰、简洁的方式来实现数组扁平化，替代了过去复杂的递归或循环实现。",
      importance: "critical",
    },
    {
      feature: "可控的扁平化深度",
      description: "通过 `depth` 参数，可以精确控制扁平化的层级，从单层展开到无限深度展开，提供了极大的灵活性。",
      importance: "high",
    },
    {
      feature: "自动处理空槽",
      description: "在扁平化过程中会自动移除数组中的空槽（empty slots），这使得数据清理工作变得更加简单。",
      importance: "medium",
    },
     {
      feature: "非破坏性操作",
      description: "flat() 方法返回一个新数组，而不会修改调用它的原始数组，符合函数式编程的不可变性原则。",
      importance: "high",
    },
  ],

  commonUseCases: [
    {
      title: '清理和规范化API响应',
      description: '从不同API端点返回的数据结构可能不一致，使用 flat() 可以轻松地将其规范化为一维数组。',
      difficulty: 'beginner',
      code: `
function getProductTags() {
  const response1 = { data: ['Electronics', 'Gadgets'] };
  const response2 = { data: [['Wearables'], ['Smart Home']] };
  const response3 = { data: 'No Tags' }; // 某个API可能返回非数组

  const allTags = [response1.data, response2.data, response3.data];

  // 过滤掉非数组项，然后进行深度扁平化
  const flattenedTags = allTags
    .filter(Array.isArray)
    .flat(Infinity);
  
  return flattenedTags;
}

console.log(getProductTags());
// 输出: ["Electronics", "Gadgets", "Wearables", "Smart Home"]
      `,
      diagram: `
\`\`\`mermaid
sequenceDiagram
    participant App as 应用代码
    participant Data as 原始混合数据
    participant Filter as filter(Array.isArray)
    participant Flat as flat(Infinity)
    participant Result as 最终结果

    App->>Data: 传入原始数据 [tags1, tags2, 'string']
    Data->>Filter: 进行过滤
    Filter->>Data: 返回数组项 [tags1, tags2]
    Data->>Flat: 进行深度扁平化
    Flat->>Result: 返回扁平化数组
    Result-->>App: 获取最终结果
\`\`\`
      `
    },
    {
      title: '处理树形结构数据',
      description: '将一个树形结构（如评论回复）的所有节点提取到一个列表中，以便进行搜索或计数。',
      difficulty: 'intermediate',
      code: `
const comments = [
  { id: 1, text: 'Great post!', replies: [
    { id: 2, text: 'I agree!', replies: [
      { id: 3, text: 'Me too!' }
    ]}
  ]},
  { id: 4, text: 'Any thoughts on X?' }
];

function getAllComments(comments) {
  return comments.map(comment => [comment, ...getAllComments(comment.replies || [])]);
}

const allCommentsList = getAllComments(comments).flat(Infinity);

console.log('评论总数:', allCommentsList.length); // 4
console.log(allCommentsList.map(c => c.text)); 
// 输出: ["Great post!", "I agree!", "Me too!", "Any thoughts on X?"]
      `,
      diagram: `
\`\`\`mermaid
graph TD
    A[原始评论树] --> B(递归函数 getAllComments)
    B --> C[生成嵌套节点数组]
    C --> D{flat(Infinity)}
    D --> E[扁平化的评论列表]

    subgraph TreeStructure ["树形结构"]
        T1("id: 1") --> T2("id: 2")
        T2 --> T3("id: 3")
        T1_Sibling("id: 4")
    end

    subgraph FlattenedAfter ["扁平化后"]
        E1[Comment 1]
        E2[Comment 2]
        E3[Comment 3]
        E4[Comment 4]
    end

    style A fill:#e1f5fe,stroke:#01579b
    style E fill:#e8f5e8,stroke:#1b5e20
\`\`\`
`
    },
    {
      title: '动态生成React组件列表',
      description: '在React中，有时组件可能会返回一个数组。使用 flat() 可以确保最终渲染的列表是扁平的。',
      difficulty: 'intermediate',
      code: `
// 伪代码，演示概念
function RenderItems({ items }) {
  // someCondition 可能使某些项渲染为组件数组
  const list = items.map(item => {
    if (item.someCondition) {
      return [<ComponentA key={item.id}/>, <ComponentB key={item.id + '_b'}/>];
    }
    return <ComponentC key={item.id}/>;
  });
  
  // list 可能是这样的: [<C/>, [<A/>, <B/>], <C/>]
  // 使用 flat() 确保渲染列表是一维的
  return <div>{list.flat()}</div>;
}
      `,
       diagram: `
\`\`\`mermaid
flowchart TD
    A[原始items数组] --> B[map循环]
    B --> C{条件判断};
    C -->|true| D["返回组件数组 [ComponentA, ComponentB]"];
    C -->|false| E["返回单个组件 ComponentC"];
    D --> F[生成嵌套的组件列表];
    E --> F;
    F --> G{list.flat()};
    G --> H[生成扁平的React元素数组];
    H --> I[React渲染];
    
    style I fill:#e8f5e8,stroke:#1b5e20
\`\`\`
       `
    }
  ],
  
  limitations: [
    "flat() 不会改变原数组，它会返回一个新数组。如果想原地修改，这不是合适的工具。",
    "对于非常巨大的多维数组，使用 `Infinity` 深度可能会消耗大量内存和计算资源。",
    "它只能扁平化数组，对于嵌套的对象或其他可迭代对象无效。",
    "`depth` 参数如果是负数或非数值，将被视为0，不会报错但也不会执行扁平化。",
  ],
  
  bestPractices: [
    "当你不确定嵌套深度时，使用 `flat(Infinity)` 是最直接的完全扁平化方法。",
    "在处理可能包含非数组项的混合数据时，先使用 `filter(Array.isArray)` 进行过滤，可以避免错误并提高效率。",
    "对于性能敏感的大型数组，明确指定一个合理的 `depth` 比使用 `Infinity` 更好。",
    "结合 `map` 方法使用 `flatMap()` 可以作为一种更高效的先映射后扁平化的操作。",
    "利用 `flat()` 可以轻松移除数组中的空槽（holes），这是一种有效的数据清理技巧。",
  ],

  warnings: [
    "在非常深的嵌套数组上使用 `flat(Infinity)` 可能会导致堆栈溢出（Stack Overflow）错误，尽管现代JS引擎对此有优化。",
    "过度使用 `flat()` 可能掩盖了数据结构设计上的问题。有时，更好的数据结构比后续的扁平化处理更重要。",
    "请记住 `flat()` 会移除空槽。如果你的逻辑依赖于这些空槽的存在（例如，`array.length`），可能会导致意外行为。",
  ],

  comparisonAnalysis: {
    title: "Array.flat() 与其他扁平化方法的对比",
    description: "深入分析 `flat()` 与传统的递归、`reduce` 以及 `flatMap` 方法在性能、可读性和使用场景上的差异。",
    comparisons: [
      {
        name: "flat() vs. 递归实现",
        advantages: [
          "代码极其简洁，可读性高。",
          "原生实现，性能通常经过高度优化。",
          "内置深度控制和空槽处理，无需手动实现。",
          "避免了自己编写递归时可能出现的堆栈溢出风险。"
        ],
        disadvantages: [
          "灵活性较低，无法在扁平化过程中执行自定义逻辑。"
        ],
        useCases: [
          "绝大多数标准的数组扁平化场景。"
        ]
      },
      {
        name: "flat() vs. reduce() 实现",
        advantages: [
          "可读性更好，意图更明确。",
          "通常性能更优，因为是原生代码。",
          "代码量更少，不易出错。"
        ],
        disadvantages: [
          "灵活性不如 `reduce`，`reduce` 可以在累积过程中执行更复杂的操作。"
        ],
        useCases: [
          "当只需要纯粹的扁平化操作时，`flat()` 是首选。"
        ]
      },
      {
        name: "flat() vs. flatMap()",
        advantages: [
          "当只需要扁平化而不需要映射时，`flat()` 的意图更清晰。"
        ],
        disadvantages: [
          "如果需要先 `map` 再 `flat`，执行两次操作效率低于一次 `flatMap`。",
        ],
        useCases: [
          "用于已经存在的嵌套数组。而`flatMap`用于在创建新数组时直接生成一维数组。"
        ]
      }
    ]
  }
};

export default basicInfo; 