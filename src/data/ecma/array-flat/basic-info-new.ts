/**
 * 📋 Tab 1: 基本信息 (basic-info.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * 🛡️ 数据一致性强约束（零容忍错误）
 * ⚠️ 生成基本信息后必须同时更新 index.ts 文件，确保数据一致性
 *
 * 🚨 强制检查清单:
 * - [ ] basic-info.ts 已更新实际内容
 * - [ ] index.ts 的 description 字段已从 basicInfo.definition 同步
 * - [ ] index.ts 的 syntax 字段已从 basicInfo.syntax 同步
 * - [ ] index.ts 的 example 字段已从 basicInfo.commonUseCases[0].code 同步
 * - [ ] index.ts 的 notes 字段已从 basicInfo.limitations[0] 同步
 * - [ ] index.ts 的 version 和 tags 字段已根据API特性设置
 * - [ ] 页面不再显示 {XXX} 格式的骨架内容
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🎯 Tab定位与价值 - 升级版
 *
 * 🎭 **身份定位**：你是一位在异步编程领域深耕多年的前辈，还记得初入行时被回调地狱折磨的痛苦
 *
 * 💡 **核心使命**：基本信息Tab是API的**承重墙识别器**，帮助开发者在3分钟内区分：
 * - 🏗️ **承重墙**：移除就会坍塌的核心概念（Promise状态、微任务队列）
 * - 🎨 **装饰品**：看着重要其实可有可无的细节（某些边缘API）
 * - 🚪 **暗门**：知道就能事半功倍的窍门（链式调用、错误传播）
 *
 * 🌊 **表达温度**：像一位愿意分享的老友，在咖啡馆里推心置腹地说：
 * "Promise这东西，表面上是异步编程的工具，实际上是JavaScript从混乱走向优雅的转折点"
 *
 * 🔍 **知识考古精神**：不满足于知道"Promise是什么"，更要挖掘"为什么会有Promise"
 * 让开发者能看见Promise诞生的那个瞬间，理解设计者夜不能寐要解决的根本问题
 *
 * 📊 内容结构要求 - 必须包含的11个核心部分：
 * - definition: 一句话定义（30-50字）- 战略定位+核心使命+存在价值+技术优势
 * - introduction: 详细介绍（100-200字）- 历史背景+设计哲学+核心功能+生态地位+技术优势
 * - syntax: 完整TypeScript语法 - 🆕 ECMAScript规范定位+TypeScript类型定义分析+源码定位+泛型解析+重载分析
 * - quickExample: 完整基础示例 - 完整可运行+中文注释+基础用法+简洁明了
 * - scenarioDiagram: 业务场景图表 - 多图表支持+业务场景+相关技术+技术特性+中文标注
 * - parameters: 详细参数说明 - 设计原理+类型约束+性能影响+最佳实践
 * - returnValue: 返回值说明 - 类型+描述+属性+示例
 * - keyFeatures: 核心特性 - 3-5个技术亮点+差异化优势+独特能力+importance等级
 * - limitations: 使用限制 - 3-5条限制+技术原因+替代方案
 * - commonUseCases: 🆕 常见用例 - 多个难度层次+完整代码示例+中文解释+Mermaid图表
 * - bestPractices: 最佳实践 - 5-8条实用建议+官方推荐+社区实践+性能优化
 * - warnings: 重要警告 - 3-5个常见错误+安全风险+性能陷阱+兼容性问题
 *
 * 🎯 质量标准
 * - 定义精准：一句话准确概括API核心价值
 * - 介绍全面：覆盖场景、特点、优势、应用
 * - 语法完整：TypeScript定义准确且完整
 * - 参数详细：每个参数都有清晰说明和示例
 * - 返回值明确：数据结构和使用方法清楚
 * - 特性突出：3-5个关键亮点明确有价值
 * - 限制客观：诚实说明使用约束和技术原因
 * - 实践实用：最佳实践基于真实经验且可执行
 * - 警告重要：警告涵盖最常见和最严重的问题
 */

import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: `Promise是ES6引入的革命性异步编程解决方案，代表一个异步操作的最终完成（成功或失败）。它是一个代理对象，用于表示一个异步操作的最终状态及其结果值。Promise有三种不可逆的状态：pending（等待中）、fulfilled（已完成）和rejected（已拒绝）。Promise通过提供链式调用、统一错误处理和状态管理，彻底解决了传统回调函数的"回调地狱"问题，为现代JavaScript异步编程奠定了基础。`,

  introduction: `Promise是ES6(ES2015)引入的异步编程核心特性，它不仅仅是一个技术工具，更是异步编程范式的根本性变革。在Promise出现之前，JavaScript的异步编程主要依赖回调函数，这导致了代码嵌套、错误处理困难、逻辑分散等问题。

Promise的设计灵感来源于函数式编程中的"Future"和"Deferred"概念，它将异步操作抽象为一个可以被操作的对象。这种抽象让异步代码具有了与同步代码相似的组合性和可预测性，使得复杂的异步逻辑变得清晰和可维护。

Promise不仅解决了技术问题，更重要的是它改变了开发者思考异步编程的方式。它让我们能够用声明式的方式描述异步操作的流程，而不是用命令式的回调来处理异步结果。这种思维转变为后续的async/await语法、现代前端框架的异步处理、以及整个JavaScript生态系统的发展奠定了基础。`,

  syntax: `
// 🆕 ECMAScript规范定位信息
/**
 * 规范定义位置：
 * - ECMAScript 2015 (ES6) 规范：25.4 Promise Objects
 * - Promise构造函数：25.4.3 The Promise Constructor
 * - Promise.prototype方法：25.4.5 Properties of the Promise Prototype Object
 * - Promise静态方法：25.4.4 Properties of the Promise Constructor
 * - 执行模型：8.4 Jobs and Job Queues (微任务队列)
 * - MDN文档：https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise
 */

// === 🆕 TypeScript类型定义分析 ===
/**
 * Promise类型定义位置：
 * - 核心类型：lib.es2015.promise.d.ts
 * - 扩展类型：lib.es2018.promise.d.ts (finally方法)
 * - 最新类型：lib.es2021.promise.d.ts (any方法)
 */

// Promise构造函数的完整TypeScript签名
interface PromiseConstructor {
  new <T>(executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void): Promise<T>;

  // 静态方法类型定义
  resolve(): Promise<void>;
  resolve<T>(value: T | PromiseLike<T>): Promise<T>;
  reject<T = never>(reason?: any): Promise<T>;

  all<T extends readonly unknown[] | []>(values: T): Promise<{ -readonly [P in keyof T]: Awaited<T[P]> }>;
  allSettled<T extends readonly unknown[] | []>(values: T): Promise<{ -readonly [P in keyof T]: PromiseSettledResult<Awaited<T[P]>> }>;
  race<T extends readonly unknown[] | []>(values: T): Promise<Awaited<T[number]>>;
  any<T extends readonly unknown[] | []>(values: T): Promise<Awaited<T[number]>>;
}

// Promise实例的完整TypeScript签名
interface Promise<T> {
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): Promise<TResult1 | TResult2>;

  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): Promise<T | TResult>;

  finally(onfinally?: (() => void) | undefined | null): Promise<T>;
}

// === 基础语法形式 ===

// 1. Promise构造函数 - 标准形式
const promise = new Promise<string>((resolve, reject) => {
  // executor函数：立即同步执行
  // resolve: 将Promise状态改为fulfilled
  // reject: 将Promise状态改为rejected

  try {
    // 异步操作示例
    setTimeout(() => {
      const success = Math.random() > 0.5;
      if (success) {
        resolve('操作成功'); // 传递成功值
      } else {
        reject(new Error('操作失败')); // 传递错误原因
      }
    }, 1000);
  } catch (error) {
    // 同步错误也会被reject
    reject(error);
  }
});

// 2. Promise实例方法 - 链式调用
promise
  .then(
    // onFulfilled: 成功回调
    (value: string) => {
      console.log('成功:', value);
      return value.toUpperCase(); // 返回转换后的值
    },
    // onRejected: 失败回调（可选，通常用catch代替）
    (reason: any) => {
      console.error('失败:', reason);
      return 'DEFAULT'; // 返回默认值
    }
  )
  .then((transformedValue: string) => {
    // 处理上一个then的返回值
    console.log('转换后:', transformedValue);

    // 可以返回新的Promise
    return new Promise<number>((resolve) => {
      setTimeout(() => resolve(transformedValue.length), 500);
    });
  })
  .catch((error: any) => {
    // 捕获Promise链中的任何错误
    console.error('链式调用错误:', error);
    return 0; // 返回默认值，继续链式调用
  })
  .finally(() => {
    // 清理操作：无论成功失败都会执行
    console.log('Promise链执行完成');
  });

// === 🆕 Promise静态方法详解 ===

// 3. Promise.resolve - 创建已解决的Promise
Promise.resolve(42);                    // Promise<number>
Promise.resolve(Promise.resolve(42));   // Promise<number> (自动展开)
Promise.resolve({ then: (resolve) => resolve(42) }); // 处理thenable对象

// 4. Promise.reject - 创建已拒绝的Promise
Promise.reject(new Error('失败'));      // Promise<never>
Promise.reject('字符串错误');           // Promise<never>

// === 🆕 并发控制方法深度解析 ===

// 5. Promise.all - 全部成功才成功
const allPromise = Promise.all([
  Promise.resolve(1),
  Promise.resolve('hello'),
  Promise.resolve(true)
]); // Promise<[number, string, boolean]>

// 类型安全的Promise.all使用
async function typedPromiseAll() {
  const [num, str, bool] = await Promise.all([
    fetchNumber(),      // Promise<number>
    fetchString(),      // Promise<string>
    fetchBoolean()      // Promise<boolean>
  ]);
  // TypeScript自动推导类型
  console.log(num + 1, str.toUpperCase(), !bool);
}

// 6. Promise.allSettled - 等待全部完成
const settledPromise = Promise.allSettled([
  Promise.resolve(1),
  Promise.reject('error'),
  Promise.resolve(3)
]); // Promise<[PromiseSettledResult<number>, PromiseSettledResult<never>, PromiseSettledResult<number>]>

// 处理allSettled结果
settledPromise.then(results => {
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      console.log('Promise ' + index + ' 成功:', result.value);
    } else {
      console.log('Promise ' + index + ' 失败:', result.reason);
    }
  });
});

// 7. Promise.race - 竞速，第一个完成的获胜
const racePromise = Promise.race([
  new Promise(resolve => setTimeout(() => resolve('fast'), 100)),
  new Promise(resolve => setTimeout(() => resolve('slow'), 200))
]); // Promise<string>

// 8. Promise.any - 第一个成功的获胜（ES2021）
const anyPromise = Promise.any([
  Promise.reject('error1'),
  Promise.resolve('success'),
  Promise.reject('error2')
]); // Promise<string>

// === 🆕 高级语法模式和边界情况 ===

// 9. 嵌套Promise的自动展开
const nestedPromise = Promise.resolve(Promise.resolve(42));
// 结果是Promise<number>，不是Promise<Promise<number>>

// 10. Thenable对象的处理
const thenable = {
  then(resolve: (value: number) => void, reject: (reason: any) => void) {
    setTimeout(() => resolve(42), 100);
  }
};
Promise.resolve(thenable); // Promise<number>

// 11. 条件Promise链
function conditionalChain(needsProcessing: boolean) {
  return Promise.resolve('initial')
    .then(value => {
      if (needsProcessing) {
        // 返回Promise
        return new Promise<string>(resolve => {
          setTimeout(() => resolve(value + ' processed'), 100);
        });
      }
      // 返回普通值
      return value + ' direct';
    })
    .then(finalValue => {
      console.log('最终结果:', finalValue);
      return finalValue;
    });
}

// 12. 错误恢复和重试模式
function errorRecoveryChain() {
  return fetchData()
    .catch(error => {
      console.warn('主要数据源失败，尝试备用源');
      return fetchBackupData();
    })
    .catch(error => {
      console.warn('备用数据源也失败，使用缓存');
      return getCachedData();
    })
    .catch(error => {
      console.error('所有数据源都失败');
      return getDefaultData();
    });
}

// 13. Promise链中的异常处理
function exceptionHandling() {
  return Promise.resolve('start')
    .then(value => {
      // 同步异常会被自动catch
      throw new Error('同步错误');
    })
    .then(value => {
      // 这个then不会执行
      console.log('不会执行');
    })
    .catch(error => {
      console.log('捕获到错误:', error.message);
      // 可以恢复Promise链
      return 'recovered';
    })
    .then(value => {
      // 这个then会执行
      console.log('恢复后的值:', value);
    });
}

// === 🆕 性能和内存优化模式 ===

// 14. 避免Promise构造函数反模式
// ❌ 错误：不必要的Promise包装
function badPromiseUsage() {
  return new Promise((resolve, reject) => {
    someAsyncFunction()
      .then(result => resolve(result))
      .catch(error => reject(error));
  });
}

// ✅ 正确：直接返回Promise
function goodPromiseUsage() {
  return someAsyncFunction();
}

// 15. 内存泄漏预防
function memoryLeakPrevention() {
  let cleanup: (() => void) | null = null;

  return new Promise<string>((resolve, reject) => {
    const timer = setTimeout(() => {
      resolve('completed');
    }, 1000);

    // 保存清理函数
    cleanup = () => {
      clearTimeout(timer);
    };
  }).finally(() => {
    // 确保清理资源
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });
}
  `,

  quickExample: `
// 1. 基础Promise创建和使用
function fetchUserData(userId) {
  return new Promise((resolve, reject) => {
    // 模拟异步网络请求
    console.log(\`开始获取用户 \${userId} 的数据...\`);

    setTimeout(() => {
      if (userId > 0) {
        const userData = {
          id: userId,
          name: 'Alice Johnson',
          email: '<EMAIL>',
          avatar: 'https://example.com/avatar.jpg'
        };
        console.log('✅ 用户数据获取成功');
        resolve(userData);
      } else {
        const error = new Error('❌ 无效的用户ID');
        console.log('❌ 用户数据获取失败');
        reject(error);
      }
    }, 1000);
  });
}

// 基础链式调用
fetchUserData(123)
  .then(user => {
    console.log('👤 用户信息:', user.name);
    // 返回新的Promise
    return fetchUserPosts(user.id);
  })
  .then(posts => {
    console.log(\`📝 用户有 \${posts.length} 篇文章\`);
    // 返回普通值
    return posts.filter(post => post.published);
  })
  .then(publishedPosts => {
    console.log(\`📰 已发布文章: \${publishedPosts.length} 篇\`);
    return publishedPosts.length;
  })
  .catch(error => {
    console.error('💥 获取数据失败:', error.message);
    return 0; // 返回默认值，继续链式调用
  })
  .finally(() => {
    console.log('🏁 数据获取流程完成');
  });`,

  quickExampleDiagram: `
sequenceDiagram
    participant Client as 客户端代码
    participant Promise as Promise对象
    participant API as 模拟API
    participant Console as 控制台输出

    Note over Client: 基础Promise链式调用示例

    Client->>Promise: fetchUserData(123)
    Promise->>API: 发起异步请求
    API-->>Console: 开始获取用户数据

    Note over API: 模拟1秒网络延迟

    API->>Promise: resolve(userData)
    Promise->>Client: .then(user => {...})
    Client-->>Console: 用户信息获取成功

    Client->>Promise: return fetchUserPosts(user.id)
    Promise->>API: 获取用户文章
    API->>Promise: resolve(posts)
    Promise->>Client: .then(posts => {...})
    Client-->>Console: 用户文章获取成功

    Client->>Client: 过滤已发布文章
    Client-->>Console: 已发布文章处理完成

    Note over Client: 如果任何步骤失败
    Promise->>Client: .catch(error => {...})
    Client-->>Console: 获取数据失败

    Promise->>Client: .finally(() => {...})
    Client-->>Console: 数据获取流程完成
  `,

  quickExampleCode: `// 1. 基础Promise创建和链式调用
function fetchUserData(userId) {
  return new Promise((resolve, reject) => {
    // 模拟异步网络请求
    console.log(\`开始获取用户 \${userId} 的数据...\`);

    setTimeout(() => {
      if (userId > 0) {
        const userData = {
          id: userId,
          name: 'Alice Johnson',
          email: '<EMAIL>',
          avatar: 'https://example.com/avatar.jpg'
        };
        console.log('✅ 用户数据获取成功');
        resolve(userData);
      } else {
        const error = new Error('❌ 无效的用户ID');
        console.log('❌ 用户数据获取失败');
        reject(error);
      }
    }, 1000);
  });
}

// 基础链式调用
fetchUserData(123)
  .then(user => {
    console.log('👤 用户信息:', user.name);
    // 返回新的Promise
    return fetchUserPosts(user.id);
  })
  .then(posts => {
    console.log(\`📝 用户有 \${posts.length} 篇文章\`);
    // 返回普通值
    return posts.filter(post => post.published);
  })
  .then(publishedPosts => {
    console.log(\`📰 已发布文章: \${publishedPosts.length} 篇\`);
    return publishedPosts.length;
  })
  .catch(error => {
    console.error('💥 获取数据失败:', error.message);
    return 0; // 返回默认值，继续链式调用
  })
  .finally(() => {
    console.log('🏁 数据获取流程完成');
  });

// 2. Promise.all - 并行处理多个异步操作
async function loadUserDashboard(userId) {
  console.log('🚀 开始加载用户仪表板...');

  const promises = [
    fetchUserData(userId),
    fetchUserPosts(userId),
    fetchUserComments(userId),
    fetchUserFollowers(userId)
  ];

  try {
    const [user, posts, comments, followers] = await Promise.all(promises);

    console.log('✅ 所有数据加载完成!');
    return {
      user,
      stats: {
        posts: posts.length,
        comments: comments.length,
        followers: followers.length
      }
    };
  } catch (error) {
    console.error('❌ 仪表板加载失败:', error.message);
    throw error;
  }
}`,

  parallelExampleDiagram: `
graph TD
    A[loadUserDashboard开始] --> B[创建4个并行Promise]

    B --> C[fetchUserData]
    B --> D[fetchUserPosts]
    B --> E[fetchUserComments]
    B --> F[fetchUserFollowers]

    C --> G[Promise.all等待]
    D --> G
    E --> G
    F --> G

    G --> H{所有Promise都成功?}

    H -->|是| I[解构赋值获取结果]
    H -->|否| J[catch捕获第一个错误]

    I --> K[组装仪表板数据]
    K --> L[返回完整数据]

    J --> M[抛出错误]

    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style L fill:#e8f5e8
    style M fill:#ffebee

    classDef promiseNode fill:#fff3e0,stroke:#ef6c00
    classDef successNode fill:#e8f5e8,stroke:#2e7d32
    classDef errorNode fill:#ffebee,stroke:#c62828

    class C,D,E,F promiseNode
    class I,K,L successNode
    class J,M errorNode
  `,

  parallelExampleCode: `// 2. Promise.all - 并行处理多个异步操作
async function loadUserDashboard(userId) {
  console.log('🚀 开始加载用户仪表板...');

  const promises = [
    fetchUserData(userId),
    fetchUserPosts(userId),
    fetchUserComments(userId),
    fetchUserFollowers(userId)
  ];

  try {
    const [user, posts, comments, followers] = await Promise.all(promises);

    console.log('✅ 所有数据加载完成!');
    return {
      user,
      stats: {
        posts: posts.length,
        comments: comments.length,
        followers: followers.length
      }
    };
  } catch (error) {
    console.error('❌ 仪表板加载失败:', error.message);
    throw error;
  }
}

// 3. Promise.allSettled - 处理部分失败的场景
function loadUserDataWithFallback(userId) {
  const promises = [
    fetchUserData(userId),
    fetchUserPosts(userId).catch(() => []), // 失败时返回空数组
    fetchUserComments(userId).catch(() => []),
    fetchUserSettings(userId).catch(() => ({})) // 失败时返回空对象
  ];

  return Promise.allSettled(promises)
    .then(results => {
      const [userResult, postsResult, commentsResult, settingsResult] = results;

      // 检查关键数据是否成功
      if (userResult.status === 'rejected') {
        throw new Error('无法获取用户基本信息');
      }

      return {
        user: userResult.value,
        posts: postsResult.status === 'fulfilled' ? postsResult.value : [],
        comments: commentsResult.status === 'fulfilled' ? commentsResult.value : [],
        settings: settingsResult.status === 'fulfilled' ? settingsResult.value : {},
        warnings: results
          .filter(result => result.status === 'rejected')
          .map(result => result.reason.message)
      };
    });
}

// 4. Promise.race - 超时控制
function fetchWithTimeout(url, timeout = 5000) {
  const fetchPromise = fetch(url).then(response => response.json());

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(\`请求超时: \${timeout}ms\`));
    }, timeout);
  });

  return Promise.race([fetchPromise, timeoutPromise]);
}

// 使用超时控制
fetchWithTimeout('/api/users/123', 3000)
  .then(data => {
    console.log('✅ 数据获取成功:', data);
  })
  .catch(error => {
    if (error.message.includes('超时')) {
      console.error('⏰ 请求超时，请检查网络连接');
    } else {
      console.error('❌ 请求失败:', error.message);
    }
  });

// 5. 错误处理和恢复
function robustDataFetch(userId) {
  return fetchUserData(userId)
    .catch(error => {
      console.warn('⚠️ 主数据源失败，尝试备用数据源...');
      return fetchUserDataFromBackup(userId);
    })
    .catch(error => {
      console.warn('⚠️ 备用数据源也失败，使用缓存数据...');
      return getCachedUserData(userId);
    })
    .catch(error => {
      console.error('💥 所有数据源都失败了');
      // 返回默认用户对象
      return {
        id: userId,
        name: '未知用户',
        email: '',
        isDefault: true
      };
    });
}

// 6. Promise链中的条件处理
function processUserData(userId) {
  return fetchUserData(userId)
    .then(user => {
      if (user.needsVerification) {
        console.log('🔐 用户需要验证，发送验证邮件...');
        return sendVerificationEmail(user.email)
          .then(() => ({ ...user, verificationSent: true }));
      }
      return user;
    })
    .then(user => {
      if (user.isVip) {
        console.log('👑 VIP用户，加载特殊权限...');
        return loadVipPermissions(user.id)
          .then(permissions => ({ ...user, permissions }));
      }
      return user;
    })
    .then(user => {
      console.log('✅ 用户数据处理完成:', user.name);
      return user;
    });
}

// 使用示例
processUserData(123)
  .then(user => {
    console.log('最终用户数据:', user);
  })
  .catch(error => {
    console.error('处理失败:', error.message);
  });
  `,

  parameters: [
    {
      name: "executor",
      type: "Function",
      required: true,
      description: "Promise构造函数的执行器函数，接收resolve和reject两个参数",
      details: "(resolve: Function, reject: Function) => void"
    },
    {
      name: "resolve",
      type: "Function",
      required: true,
      description: "成功回调函数，用于将Promise状态改为fulfilled",
      details: "resolve(value: any) => void"
    },
    {
      name: "reject",
      type: "Function",
      required: true,
      description: "失败回调函数，用于将Promise状态改为rejected",
      details: "reject(reason: any) => void"
    },
    {
      name: "onFulfilled",
      type: "Function",
      required: false,
      description: "then方法的成功回调函数",
      details: "(value: any) => any | Promise"
    },
    {
      name: "onRejected",
      type: "Function",
      required: false,
      description: "then方法的失败回调函数或catch方法的回调",
      details: "(reason: any) => any | Promise"
    },
    {
      name: "onFinally",
      type: "Function",
      required: false,
      description: "finally方法的回调函数，无论成功失败都会执行",
      details: "() => void"
    }
  ],

  returnValue: {
    type: "Promise",
    description: "Promise构造函数返回Promise实例，then/catch/finally方法返回新的Promise实例，支持链式调用"
  },

  coreFeatures: [
    {
      feature: "三态状态机",
      description: "Promise有三种不可逆状态：pending（等待）、fulfilled（成功）、rejected（失败）",
      importance: "critical" as const,
      details: "状态转换是单向的：pending → fulfilled 或 pending → rejected，一旦改变就不可逆转，确保了异步操作的可预测性和一致性"
    },
    {
      feature: "链式调用机制",
      description: "通过then/catch/finally方法实现优雅的链式调用，彻底解决回调地狱",
      importance: "critical" as const,
      details: "每个方法都返回新的Promise实例，支持无限链式调用、值传递、错误传播和异步操作的串联"
    },
    {
      feature: "统一错误处理",
      description: "提供统一的错误处理机制，错误会自动沿着Promise链传播",
      importance: "critical" as const,
      details: "任何环节的错误都会被传播到最近的catch处理器，避免了传统回调中错误处理的复杂性"
    },
    {
      feature: "微任务调度",
      description: "Promise回调在微任务队列中执行，确保正确的执行顺序",
      importance: "high" as const,
      details: "Promise的then/catch回调会被添加到微任务队列，优先级高于宏任务，确保了可预测的执行顺序"
    },
    {
      feature: "值传递和转换",
      description: "支持在Promise链中传递和转换值，包括同步值和异步Promise",
      importance: "high" as const,
      details: "then方法可以返回普通值或新的Promise，自动处理值的传递和Promise的展开"
    },
    {
      feature: "并发控制静态方法",
      description: "提供Promise.all、Promise.race、Promise.allSettled、Promise.any等强大的并发控制方法",
      importance: "high" as const,
      details: "支持多个Promise的并行执行、竞速、部分失败处理等复杂的异步编排场景"
    }
  ],

  keyFeatures: [
    {
      feature: "异步操作的标准化封装",
      description: "将各种异步操作（网络请求、定时器、文件I/O等）统一封装为Promise对象",
      importance: "critical" as const,
      details: "提供了统一的异步操作接口，让不同类型的异步操作具有相同的处理方式和API"
    },
    {
      feature: "thenable接口兼容性",
      description: "支持thenable对象，与其他Promise实现和类Promise对象兼容",
      importance: "high" as const,
      details: "任何具有then方法的对象都可以被Promise.resolve()转换为标准Promise"
    },
    {
      feature: "自动Promise展开",
      description: "当then回调返回Promise时，会自动展开，避免Promise嵌套",
      importance: "high" as const,
      details: "防止Promise<Promise<T>>的嵌套结构，始终保持Promise<T>的扁平结构"
    },
    {
      feature: "错误边界和恢复",
      description: "catch方法不仅能捕获错误，还能返回恢复值继续Promise链",
      importance: "high" as const,
      details: "支持错误恢复模式，让Promise链在遇到错误后能够继续执行"
    },
    {
      feature: "资源清理保证",
      description: "finally方法确保无论成功失败都能执行清理操作",
      importance: "medium" as const,
      details: "类似于try-catch-finally中的finally，保证资源清理代码一定会执行"
    },
    {
      feature: "组合性和可测试性",
      description: "Promise具有良好的组合性，易于单元测试和模拟",
      importance: "medium" as const,
      details: "可以轻松组合多个Promise，支持依赖注入和测试替身模式"
    }
  ],

  commonUseCases: [
    {
      title: 'API请求和数据获取',
      description: '使用Promise封装网络请求，处理异步数据获取和错误处理',
      difficulty: 'beginner' as const,
      explanation: '这个案例展示了如何使用Promise封装fetch请求，实现统一的错误处理和响应格式化。',
      code: `
// 封装fetch请求
function apiRequest(url, options = {}) {
  return fetch(url, {
    headers: { 'Content-Type': 'application/json' },
    ...options
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
    }
    return response.json();
  })
  .catch(error => {
    console.error('API请求失败:', error);
    throw error;
  });
}

// 使用示例
apiRequest('/api/users/123')
  .then(user => console.log('用户数据:', user))
  .catch(error => console.error('获取用户失败:', error));
      `,
      diagram: `
sequenceDiagram
    participant App as 应用程序
    participant API as apiRequest函数
    participant Server as 服务器
    participant Console as 控制台

    App->>API: apiRequest('/api/users/123')
    API->>Server: fetch请求

    alt 请求成功
        Server-->>API: 200 OK + JSON数据
        API->>API: response.json()
        API-->>App: 返回用户数据
        App->>Console: console.log('用户数据:', user)
    else 请求失败
        Server-->>API: 4xx/5xx 错误
        API->>API: throw new Error()
        API-->>App: 抛出错误
        App->>Console: console.error('获取用户失败')
    end
      `
    },
    {
      title: '并行数据加载',
      description: '使用Promise.all同时加载多个数据源，提高页面加载性能',
      difficulty: 'intermediate' as const,
      explanation: '这个案例展示了Promise.all的强大之处：并行执行多个异步操作，显著提升数据加载速度。',
      code: `
// 并行加载页面所需的所有数据
async function loadPageData(userId) {
  try {
    const [user, posts, comments, notifications] = await Promise.all([
      fetchUser(userId),
      fetchUserPosts(userId),
      fetchUserComments(userId),
      fetchNotifications(userId)
    ]);

    return {
      user,
      posts,
      comments,
      notifications,
      loadTime: Date.now()
    };
  } catch (error) {
    console.error('页面数据加载失败:', error);
    throw error;
  }
}
      `,
      diagram: `
graph TD
    A[loadPageData开始] --> B[创建4个并行Promise]

    B --> C[fetchUser]
    B --> D[fetchUserPosts]
    B --> E[fetchUserComments]
    B --> F[fetchNotifications]

    C --> G[Promise.all等待]
    D --> G
    E --> G
    F --> G

    G --> H{所有Promise完成?}

    H -->|全部成功| I[解构赋值获取结果]
    H -->|任一失败| J[catch捕获错误]

    I --> K[组装返回数据]
    K --> L[返回完整页面数据]

    J --> M[记录错误日志]
    M --> N[抛出错误]

    style A fill:#e1f5fe,stroke:#01579b
    style G fill:#f3e5f5,stroke:#4a148c
    style L fill:#e8f5e8,stroke:#1b5e20
    style N fill:#ffebee,stroke:#c62828

    classDef promiseNode fill:#fff3e0,stroke:#ef6c00
    classDef successNode fill:#e8f5e8,stroke:#2e7d32
    classDef errorNode fill:#ffebee,stroke:#c62828

    class C,D,E,F promiseNode
    class I,K,L successNode
    class J,M,N errorNode
      `
    },
    {
      title: '超时控制和重试机制',
      description: '实现请求超时控制和自动重试，提高应用的健壮性',
      difficulty: 'advanced' as const,
      explanation: '这个案例展示了Promise的高级应用：结合Promise.race实现超时控制，使用循环和延时实现智能重试机制。',
      code: `
// 带超时的Promise
function withTimeout(promise, timeout) {
  return Promise.race([
    promise,
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('操作超时')), timeout)
    )
  ]);
}

// 重试机制
async function retryOperation(operation, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}

// 使用示例
retryOperation(() => withTimeout(fetchData(), 5000))
  .then(data => console.log('数据获取成功:', data))
  .catch(error => console.error('重试失败:', error));
      `,
      diagram: `
flowchart TD
    A[开始retryOperation] --> B[尝试次数 i = 0]
    B --> C[执行withTimeout操作]

    C --> D[Promise.race竞速]
    D --> E[原始Promise]
    D --> F[超时Promise]

    E --> G{操作是否成功?}
    F --> H[超时错误]

    G -->|成功| I[返回结果]
    G -->|失败| J{是否达到最大重试次数?}
    H --> J

    J -->|是| K[抛出最终错误]
    J -->|否| L[等待延时]

    L --> M["延时计算"]
    M --> N[i++]
    N --> C

    I --> O[console.log成功]
    K --> P[console.error失败]

    style A fill:#e1f5fe,stroke:#01579b
    style D fill:#f3e5f5,stroke:#4a148c
    style I fill:#e8f5e8,stroke:#1b5e20
    style K fill:#ffebee,stroke:#c62828
    style M fill:#fff3e0,stroke:#ef6c00

    classDef successNode fill:#e8f5e8,stroke:#2e7d32
    classDef errorNode fill:#ffebee,stroke:#c62828
    classDef processNode fill:#fff3e0,stroke:#ef6c00
    classDef raceNode fill:#f3e5f5,stroke:#4a148c

    class I,O successNode
    class H,K,P errorNode
    class L,M,N processNode
    class D,E,F raceNode
      `
    }
  ],

  limitations: [
    "Promise一旦创建就会立即执行executor函数，无法取消或暂停",
    "单个Promise只能resolve一个值，不支持多值流或持续的数据流",
    "Promise状态不可逆，无法重置或重新执行",
    "缺乏内置的取消机制，需要额外的AbortController等方案",
    "错误处理必须显式添加catch，否则会产生未捕获的Promise rejection警告",
    "Promise链过长时仍可能影响代码可读性和调试难度",
    "不支持同步操作的直接Promise化，需要手动包装",
    "内存泄漏风险：长时间pending的Promise可能导致内存泄漏"
  ],

  bestPractices: [
    // === 🎯 错误处理最佳实践 ===
    "【错误捕获】始终为Promise链添加catch处理错误：promise.then().catch()，避免未捕获的rejection警告",
    "【同步错误】在Promise构造函数中使用try-catch包装同步代码：防止同步异常绕过Promise错误处理",
    "【错误传播】利用Promise的错误传播机制：错误会自动跳过then回调，直到遇到catch处理器",
    "【错误恢复】在catch中返回值来恢复Promise链：catch(() => 'default').then(value => process(value))",
    "【错误分类】根据错误类型进行不同处理：网络错误重试，业务错误提示，系统错误上报",

    // === 🚀 性能优化最佳实践 ===
    "【并行执行】使用Promise.all()处理独立的并行异步操作：const [a, b, c] = await Promise.all([fetchA(), fetchB(), fetchC()])",
    "【部分失败】使用Promise.allSettled()处理部分失败可接受的场景：获取多个数据源，部分失败不影响整体",
    "【竞速控制】合理使用Promise.race()实现超时控制：Promise.race([fetchData(), timeout(5000)])",
    "【批处理优化】避免在循环中创建大量Promise：使用批处理或限流控制并发数量",
    "【内存管理】及时清理长时间pending的Promise：避免内存泄漏，特别是在SPA应用中",

    // === 📝 代码组织最佳实践 ===
    "【语法选择】优先使用async/await语法：提高代码可读性，错误处理更直观",
    "【链式调用】在函数式编程场景中使用Promise链：data.then(transform).then(validate).then(save)",
    "【避免嵌套】避免Promise构造函数反模式：直接返回已有的Promise，不要不必要地包装",
    "【命名清晰】为复杂的Promise操作提供清晰的函数名：fetchUserDataWithRetry, validateAndSaveUser",
    "【类型安全】在TypeScript中明确Promise的泛型类型：Promise<User>而不是Promise<any>",

    // === 🔄 异步流程控制最佳实践 ===
    "【串行执行】需要依赖关系时使用串行：const user = await fetchUser(); const posts = await fetchUserPosts(user.id)",
    "【条件异步】在Promise链中处理条件逻辑：.then(data => needsProcess ? processData(data) : data)",
    "【循环异步】使用for...of或reduce处理异步循环：避免forEach中的异步陷阱",
    "【重试机制】实现智能重试：指数退避、最大重试次数、可恢复错误判断",
    "【超时控制】为所有网络请求添加超时：防止无限等待，提升用户体验",

    // === 🛠️ 资源管理最佳实践 ===
    "【资源清理】使用finally()确保资源清理：无论成功失败都要释放资源",
    "【取消机制】为长时间运行的Promise提供取消机制：结合AbortController实现请求取消",
    "【连接池】在Node.js中合理配置HTTP连接池：避免连接泄漏和资源耗尽",
    "【缓存策略】实现Promise结果缓存：避免重复的异步操作，提升性能",
    "【垃圾回收】避免闭包中的循环引用：及时清理事件监听器和定时器",

    // === 🧪 测试和调试最佳实践 ===
    "【单元测试】使用Promise.resolve()/reject()创建测试用Promise：便于模拟各种异步场景",
    "【测试工具】使用async/await编写测试：测试代码更清晰，错误信息更准确",
    "【调试技巧】在Promise链中添加调试点：.then(data => { console.log('debug:', data); return data; })",
    "【错误追踪】保持错误堆栈信息：避免在catch中丢失原始错误信息",
    "【性能监控】监控Promise的执行时间：识别性能瓶颈和异常情况",

    // === 🏗️ 架构设计最佳实践 ===
    "【API设计】设计一致的异步API：所有异步操作都返回Promise，保持接口统一",
    "【错误边界】在应用层建立Promise错误边界：统一处理未捕获的Promise rejection",
    "【状态管理】结合状态管理库使用Promise：Redux-thunk、Vuex actions等",
    "【中间件模式】实现Promise中间件：请求拦截、响应转换、错误处理等",
    "【微服务集成】在微服务架构中使用Promise：服务间通信、熔断器、负载均衡",

    // === 🔒 安全性最佳实践 ===
    "【输入验证】在Promise链中验证数据：防止恶意输入和数据污染",
    "【敏感信息】避免在Promise中泄露敏感信息：错误消息、日志记录等",
    "【CSRF防护】在网络请求中实现CSRF防护：token验证、同源检查等",
    "【速率限制】实现客户端速率限制：防止API滥用和DDoS攻击",
    "【数据加密】在传输敏感数据时使用加密：HTTPS、数据签名等",

    // === 🌐 跨平台兼容性最佳实践 ===
    "【Polyfill使用】在旧浏览器中使用Promise polyfill：确保兼容性",
    "【Node.js适配】在Node.js中正确处理Promise：unhandledRejection事件监听",
    "【React Native】在React Native中优化Promise使用：避免阻塞UI线程",
    "【Web Workers】在Web Workers中使用Promise：处理计算密集型任务",
    "【Service Worker】在Service Worker中实现Promise缓存：离线功能、推送通知等"
  ],

  warnings: [
    "忘记添加catch可能导致未捕获的Promise rejection，在Node.js中可能导致进程崩溃",
    "在Promise构造函数中抛出同步错误需要用try-catch包装，否则会绕过Promise的错误处理",
    "Promise.all()中任何一个Promise失败都会导致整体失败，考虑使用Promise.allSettled()",
    "避免在Promise链中返回非Promise值时忘记return，这会导致undefined传递给下一个then",
    "注意Promise的微任务特性，可能会影响代码的执行顺序",
    "长时间pending的Promise可能导致内存泄漏，特别是在SPA应用中",
    "Promise构造函数的executor会立即执行，不要在其中放置昂贵的同步操作"
  ],

  notes: [
    'Promise是ES6最重要的特性之一，彻底改变了JavaScript异步编程的方式',
    'Promise是async/await语法的基础，理解Promise对掌握现代异步编程至关重要',
    'Promise的微任务特性确保了回调的执行顺序，是事件循环机制的重要组成部分',
    'Promise.all、Promise.race等静态方法提供了强大的并发控制能力',
    'Promise的链式调用和错误传播机制大大简化了复杂异步逻辑的处理',
    'Promise是现代前端框架（React、Vue）和工具库（axios、fetch）的基础技术'
  ],

  mermaidDiagram: `
graph TD
    A[Promise创建] --> B[executor函数执行]
    B --> C{异步操作}
    C -->|成功| D[resolve调用]
    C -->|失败| E[reject调用]

    D --> F[fulfilled状态]
    E --> G[rejected状态]

    F --> H[then回调执行]
    G --> I[catch回调执行]

    H --> J{返回值类型}
    I --> J

    J -->|普通值| K[新Promise fulfilled]
    J -->|Promise| L[Promise展开]
    J -->|抛出错误| M[新Promise rejected]

    K --> N[链式调用继续]
    L --> N
    M --> O[错误传播]

    N --> P[finally执行]
    O --> P
    P --> Q[Promise链完成]

    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style G fill:#ffebee
    style Q fill:#fff3e0
  `,

  scenarioDiagrams: [
    {
      title: 'Promise异步操作管理场景',
      diagram: `
graph TD
    A[异步操作管理] --> B[网络请求处理]
    A --> C[定时器操作]
    A --> D[文件系统操作]
    A --> E[数据库查询]

    B --> B1[fetch API调用]
    B --> B2[XMLHttpRequest封装]
    B --> B3[第三方API集成]
    B --> B4[GraphQL查询]

    C --> C1[setTimeout封装]
    C --> C2[setInterval管理]
    C --> C3[动画帧控制]
    C --> C4[延迟执行]

    D --> D1[文件读取]
    D --> D2[文件写入]
    D --> D3[目录操作]
    D --> D4[流处理]

    E --> E1[SQL查询]
    E --> E2[NoSQL操作]
    E --> E3[事务处理]
    E --> E4[连接池管理]

    style A fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    style B fill:#f3e5f5,stroke:#4a148c
    style C fill:#e8f5e8,stroke:#1b5e20
    style D fill:#fff3e0,stroke:#ef6c00
    style E fill:#fce4ec,stroke:#880e4f
      `
    },
    {
      title: 'Promise错误处理机制',
      diagram: `
graph TD
    A[Promise错误处理] --> B[错误捕获]
    A --> C[错误传播]
    A --> D[错误恢复]
    A --> E[错误分类]

    B --> B1[try-catch替代]
    B --> B2[catch方法]
    B --> B3[全局错误处理]
    B --> B4[未捕获错误监听]

    C --> C1[自动向下传播]
    C --> C2[跳过then回调]
    C --> C3[到达最近catch]
    C --> C4[错误链追踪]

    D --> D1[catch返回值]
    D --> D2[错误重试机制]
    D --> D3[降级处理]
    D --> D4[默认值提供]

    E --> E1[网络错误]
    E --> E2[业务逻辑错误]
    E --> E3[系统错误]
    E --> E4[用户输入错误]

    style A fill:#ffebee,stroke:#c62828,stroke-width:3px
    style B fill:#fff3e0,stroke:#ef6c00
    style C fill:#f3e5f5,stroke:#4a148c
    style D fill:#e8f5e8,stroke:#1b5e20
    style E fill:#e1f5fe,stroke:#01579b
      `
    },
    {
      title: 'Promise状态管理流程',
      diagram: `
graph TD
    A[Promise状态管理] --> B[pending等待态]
    A --> C[fulfilled完成态]
    A --> D[rejected拒绝态]

    B --> B1[初始状态]
    B --> B2[异步操作进行中]
    B --> B3[可以转换状态]
    B --> B4[回调函数排队]

    C --> C1[操作成功完成]
    C --> C2[有成功值]
    C --> C3[状态不可逆]
    C --> C4[触发then回调]

    D --> D1[操作失败]
    D --> D2[有失败原因]
    D --> D3[状态不可逆]
    D --> D4[触发catch回调]

    B --> E{状态转换}
    E -->|resolve调用| C
    E -->|reject调用| D
    E -->|异常抛出| D

    style A fill:#fff3e0,stroke:#ef6c00,stroke-width:3px
    style B fill:#e1f5fe,stroke:#01579b
    style C fill:#e8f5e8,stroke:#1b5e20
    style D fill:#ffebee,stroke:#c62828
    style E fill:#f3e5f5,stroke:#4a148c
      `
    },
    {
      title: 'Promise链式操作模式',
      diagram: `
graph TD
    A[Promise链式操作] --> B[串行处理]
    A --> C[并行处理]
    A --> D[条件处理]
    A --> E[数据转换]

    B --> B1[then链式调用]
    B --> B2[依赖关系处理]
    B --> B3[顺序执行保证]
    B --> B4[值传递机制]

    C --> C1[Promise.all]
    C --> C2[Promise.allSettled]
    C --> C3[Promise.race]
    C --> C4[Promise.any]

    D --> D1[条件分支]
    D --> D2[动态Promise创建]
    D --> D3[可选操作处理]
    D --> D4[策略模式应用]

    E --> E1[数据映射]
    E --> E2[格式转换]
    E --> E3[类型转换]
    E --> E4[数据聚合]

    style A fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    style B fill:#e1f5fe,stroke:#01579b
    style C fill:#e8f5e8,stroke:#1b5e20
    style D fill:#fff3e0,stroke:#ef6c00
    style E fill:#fce4ec,stroke:#880e4f
      `
    }
  ],

  // 🆕 对比分析 - Promise vs 其他异步编程方案
  comparisonAnalysis: {
    title: "Promise与其他异步编程方案的全面对比",
    description: "深入分析Promise与回调函数、async/await、Observable、Generator等异步编程方案的差异，帮助开发者在不同场景下做出正确的技术选择。",
    comparisons: [
      {
        name: "Promise vs 回调函数 (Callback)",
        description: "Promise与传统回调函数的核心差异对比，展示Promise如何解决回调地狱问题",
        advantages: [
          "彻底解决回调地狱问题，支持链式调用",
          "统一的错误处理机制，错误会自动传播",
          "更好的可读性和可维护性",
          "支持Promise.all、Promise.race等强大的组合方法",
          "更容易进行单元测试和模拟",
          "避免了控制反转问题，代码执行更可预测"
        ],
        disadvantages: [
          "学习成本相对较高，需要理解Promise概念",
          "在简单场景下可能显得过于复杂",
          "早期浏览器需要polyfill支持",
          "调试时堆栈信息可能不如同步代码清晰"
        ],
        useCases: [
          "复杂的异步操作链，特别是有依赖关系的场景",
          "需要并行处理多个异步操作的场景",
          "需要统一错误处理的大型应用",
          "现代Web应用的网络请求处理",
          "需要与async/await配合使用的场景"
        ],
        performance: "性能与回调函数基本相同，但在复杂场景下由于更好的代码组织可能带来间接的性能提升",
        complexity: "初期学习成本较高，但掌握后能显著降低复杂异步逻辑的开发和维护成本"
      },
      {
        name: "Promise vs async/await",
        description: "Promise与async/await语法的对比，两者实际上是互补关系",
        advantages: [
          "Promise是async/await的基础，两者可以完美结合",
          "Promise链式调用在某些场景下更简洁",
          "Promise.all等静态方法使用更直观",
          "在函数式编程风格中Promise更自然",
          "不需要async函数包装，可以直接使用"
        ],
        disadvantages: [
          "链式调用在复杂逻辑中可读性不如async/await",
          "错误处理需要显式添加catch",
          "嵌套的Promise链仍可能影响可读性",
          "在需要条件分支的异步逻辑中不如async/await直观"
        ],
        useCases: [
          "函数式编程风格的异步处理",
          "需要使用Promise静态方法的场景",
          "简单的异步操作链",
          "作为async/await的底层实现",
          "库和框架的内部实现"
        ],
        performance: "性能完全相同，async/await只是Promise的语法糖",
        complexity: "在简单场景下Promise可能更简洁，复杂场景下async/await更易理解"
      },
      {
        name: "Promise vs Observable (RxJS)",
        description: "Promise与Observable的对比，两者适用于不同的异步场景",
        advantages: [
          "更简单的API，学习成本较低",
          "原生支持，不需要额外的库",
          "对于单次异步操作更加直观",
          "与async/await完美集成",
          "浏览器和Node.js原生支持"
        ],
        disadvantages: [
          "只能处理单个值，不支持数据流",
          "无法取消，一旦创建就会执行",
          "不支持操作符组合和数据转换",
          "无法处理持续的事件流",
          "缺乏背压(backpressure)处理机制"
        ],
        useCases: [
          "单次异步操作：网络请求、文件读取等",
          "简单的异步操作组合",
          "与async/await结合的场景",
          "不需要复杂数据流处理的应用",
          "移动端或资源受限的环境"
        ],
        performance: "在单次操作中性能更好，但在复杂数据流处理中不如Observable高效",
        complexity: "简单场景下更易使用，但在复杂响应式编程场景中功能有限"
      },
      {
        name: "Promise vs Generator + co",
        description: "Promise与Generator函数配合co库的异步方案对比",
        advantages: [
          "原生支持，不需要额外的库",
          "更广泛的浏览器支持",
          "与现代异步语法(async/await)更好的兼容性",
          "更简单的错误处理机制",
          "更好的工具链支持和调试体验"
        ],
        disadvantages: [
          "无法像Generator那样暂停和恢复执行",
          "不支持惰性求值",
          "无法实现自定义的迭代逻辑",
          "在某些复杂的控制流场景中不如Generator灵活"
        ],
        useCases: [
          "现代Web应用的异步处理",
          "需要与async/await配合的场景",
          "不需要复杂控制流的异步操作",
          "团队技能栈偏向Promise/async-await的项目",
          "需要良好工具链支持的项目"
        ],
        performance: "性能相近，但Promise有更好的引擎优化",
        complexity: "学习成本较低，Generator+co需要理解更多概念"
      },
      {
        name: "Promise vs Event Emitter",
        description: "Promise与事件发射器模式的对比分析",
        advantages: [
          "更适合单次异步操作的处理",
          "自动的错误传播机制",
          "更好的组合性和链式调用",
          "内置的状态管理(pending/fulfilled/rejected)",
          "与函数式编程风格更匹配"
        ],
        disadvantages: [
          "不适合处理多次触发的事件",
          "无法移除监听器",
          "不支持事件的命名空间",
          "无法处理事件的优先级",
          "缺乏事件的生命周期管理"
        ],
        useCases: [
          "单次异步操作：API调用、文件操作等",
          "需要链式处理的异步逻辑",
          "函数式编程风格的应用",
          "与async/await结合使用的场景",
          "不需要复杂事件管理的简单应用"
        ],
        performance: "在单次操作中更高效，但在频繁事件处理中不如Event Emitter",
        complexity: "对于单次异步操作更简单，但不适合复杂的事件驱动架构"
      }
    ],
    decisionMatrix: {
      description: "基于不同场景和需求的异步编程方案选择决策矩阵",
      scenarios: [
        {
          scenario: "单次网络请求",
          recommendation: "Promise + async/await",
          reason: "简单直观，错误处理方便，与现代JavaScript生态完美集成"
        },
        {
          scenario: "复杂的异步操作链",
          recommendation: "Promise链 或 async/await",
          reason: "Promise链适合函数式风格，async/await适合命令式风格"
        },
        {
          scenario: "并行异步操作",
          recommendation: "Promise.all/allSettled",
          reason: "原生支持，性能优秀，错误处理清晰"
        },
        {
          scenario: "持续的数据流处理",
          recommendation: "Observable (RxJS)",
          reason: "Promise不适合多值流，Observable提供丰富的操作符"
        },
        {
          scenario: "事件驱动的用户交互",
          recommendation: "Event Emitter 或 Observable",
          reason: "Promise只能处理单次操作，不适合持续的事件流"
        },
        {
          scenario: "简单的回调替换",
          recommendation: "Promise",
          reason: "直接替换回调，解决回调地狱，学习成本低"
        },
        {
          scenario: "复杂的响应式编程",
          recommendation: "Observable (RxJS)",
          reason: "提供丰富的操作符和强大的组合能力"
        },
        {
          scenario: "Node.js服务端开发",
          recommendation: "Promise + async/await",
          reason: "原生支持，性能好，与Node.js API完美集成"
        },
        {
          scenario: "实时数据同步",
          recommendation: "WebSocket + Observable",
          reason: "Promise不适合持续的双向通信"
        },
        {
          scenario: "文件系统操作",
          recommendation: "Promise + async/await",
          reason: "Node.js fs.promises API原生支持，代码清晰"
        }
      ]
    }
  }
};

export default basicInfo;
