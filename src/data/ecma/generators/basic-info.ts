import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `生成器函数是ES6引入的特殊函数，使用function*语法定义，可以通过yield关键字暂停和恢复执行。生成器函数返回一个生成器对象，该对象实现了迭代器协议。生成器提供了一种简洁的方式来创建迭代器，支持惰性求值、状态保持和双向通信。`,

  syntax: `// 生成器函数定义
function* generatorFunction() {
  yield value1;
  yield value2;
  return finalValue;
}

// 生成器对象方法
const generator = generatorFunction();
generator.next();           // { value: any, done: boolean }
generator.next(value);      // 向生成器传值
generator.return(value);    // 提前结束生成器
generator.throw(error);     // 向生成器抛出错误

// yield表达式
const result = yield expression;

// yield*委托
yield* anotherGenerator();
yield* iterableObject;

// 异步生成器
async function* asyncGenerator() {
  yield await promise;
}`,

  quickExample: `// 基础生成器
function* simpleGenerator() {
  console.log('开始执行');
  yield 1;
  console.log('继续执行');
  yield 2;
  console.log('即将结束');
  return 3;
}

const gen = simpleGenerator();
console.log(gen.next()); // { value: 1, done: false }
console.log(gen.next()); // { value: 2, done: false }
console.log(gen.next()); // { value: 3, done: true }

// 无限序列生成器
function* fibonacci() {
  let a = 0, b = 1;
  while (true) {
    yield a;
    [a, b] = [b, a + b];
  }
}

const fib = fibonacci();
console.log(fib.next().value); // 0
console.log(fib.next().value); // 1
console.log(fib.next().value); // 1
console.log(fib.next().value); // 2

// 双向通信
function* communicator() {
  const input1 = yield '请输入第一个值';
  const input2 = yield '请输入第二个值';
  return \`结果: \${input1 + input2}\`;
}

const comm = communicator();
console.log(comm.next());        // { value: '请输入第一个值', done: false }
console.log(comm.next(10));      // { value: '请输入第二个值', done: false }
console.log(comm.next(20));      // { value: '结果: 30', done: true }

// 生成器委托
function* inner() {
  yield 'inner1';
  yield 'inner2';
}

function* outer() {
  yield 'outer1';
  yield* inner();
  yield 'outer2';
}

for (const value of outer()) {
  console.log(value); // 'outer1', 'inner1', 'inner2', 'outer2'
}

// 错误处理
function* errorHandler() {
  try {
    yield 'normal';
    yield 'value';
  } catch (error) {
    yield \`错误: \${error.message}\`;
  }
}

const errorGen = errorHandler();
console.log(errorGen.next());                    // { value: 'normal', done: false }
console.log(errorGen.throw(new Error('测试'))); // { value: '错误: 测试', done: false }

// 异步生成器
async function* asyncNumbers() {
  for (let i = 1; i <= 3; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield i;
  }
}

async function useAsyncGenerator() {
  for await (const num of asyncNumbers()) {
    console.log(num); // 每秒输出一个数字
  }
}`,

  coreFeatures: [
    {
      feature: "yield关键字",
      description: "暂停函数执行并返回值",
      importance: "high" as const,
      details: "yield可以暂停函数执行，保持状态，并返回值给调用者"
    },
    {
      feature: "双向通信",
      description: "通过next()方法向生成器传递值",
      importance: "high" as const,
      details: "生成器可以接收外部传入的值，实现双向数据流"
    },
    {
      feature: "状态保持",
      description: "生成器在暂停期间保持局部变量状态",
      importance: "high" as const,
      details: "函数的执行上下文在yield之间保持不变"
    },
    {
      feature: "生成器委托",
      description: "使用yield*委托给其他生成器或可迭代对象",
      importance: "medium" as const,
      details: "可以组合多个生成器，实现复杂的迭代逻辑"
    }
  ],

  keyFeatures: [
    {
      feature: "惰性求值",
      description: "值按需生成，不预先计算",
      importance: "high" as const,
      details: "适合处理大数据集或无限序列"
    },
    {
      feature: "迭代器实现",
      description: "生成器自动实现迭代器协议",
      importance: "high" as const,
      details: "简化了自定义迭代器的创建"
    },
    {
      feature: "异步支持",
      description: "支持异步生成器和for await...of",
      importance: "medium" as const,
      details: "处理异步数据流的强大工具"
    }
  ],

  limitations: [
    "生成器对象是一次性的，不能重复使用",
    "yield只能在生成器函数内部使用",
    "生成器函数不能使用箭头函数语法",
    "异步生成器需要较新的JavaScript环境支持",
    "调试生成器的执行流程可能比较复杂"
  ],

  bestPractices: [
    "使用生成器实现惰性求值和无限序列",
    "利用yield*进行生成器组合",
    "合理使用双向通信功能",
    "为生成器添加适当的错误处理",
    "使用异步生成器处理异步数据流"
  ],

  warnings: [
    "生成器对象是一次性的，遍历完成后需要重新创建",
    "yield表达式的值取决于下次next()调用的参数",
    "在生成器中使用return会结束迭代"
  ]
};

export default basicInfo;
