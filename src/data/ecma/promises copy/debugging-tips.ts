/**
 * 🐛 Tab 8: 调试技巧 (debugging-tips.ts) - 核心指导原则
 *
 * 🎯 Tab定位与价值 - 调试侦探版
 *
 * 🎭 **身份定位**：你是一位在无数个深夜与Bug搏斗的调试侦探
 * 见过各种诡异的Promise问题，练就了一双火眼金睛
 *
 * 💡 **核心信念**：真正的调试智慧不在于工具的多少，而在于思路的清晰
 * 好的调试方法不是盲目尝试，而是有序推理
 *
 * 🔍 **侦探思维**：每个Bug都是一个待解的谜案
 * - 🕵️ **现场勘查**：仔细观察错误现象和上下文
 * - 🧩 **线索收集**：收集日志、堆栈、状态等关键信息
 * - 💡 **假设验证**：基于经验提出假设，逐一验证
 * - 🎯 **真相还原**：找到根本原因，而非表面症状
 *
 * 🌊 **表达温度**：像一位分享破案经验的老侦探：
 * "这种Promise状态异常，我见过好几次，通常是因为..."
 *
 * 🎨 **美学追求**：每个调试技巧都应该让人有"原来还能这样查"的惊喜
 * 不只是告诉怎么调试，更要解释为什么这样调试有效
 *
 * 📊 适用API类型
 * 此Tab主要适用于以下类型的API：
 * - 异步API: useEffect、自定义Hooks等
 * - 复杂状态API: useReducer、useContext等
 * - 性能相关API: useMemo、useCallback等
 * - 生命周期API: 涉及组件生命周期的API
 * - 错误频发API: 开发者经常使用错误的API
 *
 * 📊 内容结构要求 - 支持子tabs结构的内容组织：
 *
 * 🆕 新增：支持子tabs的结构化内容（已实现高质量内容）
 * - subTabs: 🆕 子标签数组（Promise调试的系统化方法）
 *   - key: 子标签键
 *   - title: 子标签标题
 *   - content: 内容对象
 *     - introduction: 🆕 调试方法介绍
 *     - sections: 🆕 结构化章节数组
 *       - title: 章节标题
 *       - description: 章节描述（可选）
 *       - items: 🆕 详细调试项目数组
 *         - title: 项目标题
 *         - description: 项目描述
 *         - code: 🆕 完整调试代码示例
 *         - solution: 🆕 具体解决方案
 *         - prevention: 🆕 预防措施
 *         - steps: 🆕 调试步骤数组
 *         - tips: 🆕 实用调试技巧
 *
 * 🆕 已实现调试内容：
 * - Promise状态调试和监控
 * - 异步错误追踪和定位
 * - 微任务队列调试技巧
 * - Promise链调试方法
 * - 性能调试和优化建议
 *
 * 🎯 质量标准
 * - 问题真实：基于真实开发中遇到的调试问题
 * - 方法系统：提供系统化的调试方法和流程
 * - 工具实用：介绍实用的调试工具和技巧
 * - 步骤清晰：调试步骤清晰明确，易于跟随
 * - 预防有效：提供有效的问题预防措施
 * - 案例丰富：包含丰富的实际调试案例
 *
 * 💡 调试策略
 * - 分层诊断：从表面现象到深层原因的分层诊断
 * - 工具组合：结合多种调试工具的综合使用
 * - 经验总结：总结常见问题的调试经验和模式
 * - 预防为主：强调预防性编程和错误预防
 * - 效率优先：提供高效的问题定位和解决方法
 */

import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'Promise使用中的常见错误和解决方案',
        sections: [
          {
            title: 'Promise错误处理',
            description: 'Promise错误处理相关的常见问题',
            items: [
              {
                title: 'UnhandledPromiseRejectionWarning',
                description: 'Promise被拒绝但没有被捕获',
                solution: '为所有Promise添加catch处理或使用全局错误处理器',
                prevention: '养成为Promise链添加错误处理的习惯',
                code: `// ❌ 错误：没有错误处理
fetch('/api/data')
  .then(response => response.json())
  .then(data => console.log(data));
// 如果请求失败，会产生未捕获的Promise rejection

// ✅ 正确：添加错误处理
fetch('/api/data')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => {
    console.error('请求失败:', error);
  });

// ✅ 使用async/await的错误处理
async function fetchData() {
  try {
    const response = await fetch('/api/data');
    const data = await response.json();
    console.log(data);
  } catch (error) {
    console.error('请求失败:', error);
  }
}`
              },
              {
                title: 'Promise构造函数中的错误',
                description: 'Promise构造函数中抛出的同步错误',
                solution: '使用try-catch包装Promise构造函数中的同步代码',
                prevention: '避免在Promise构造函数中执行可能抛出错误的同步代码',
                code: `// ❌ 错误：构造函数中的同步错误
const promise = new Promise((resolve, reject) => {
  const data = JSON.parse(invalidJson); // 可能抛出错误
  resolve(data);
});

// ✅ 正确：捕获同步错误
const promise = new Promise((resolve, reject) => {
  try {
    const data = JSON.parse(invalidJson);
    resolve(data);
  } catch (error) {
    reject(error);
  }
});`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试Promise相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试Promise执行和错误',
            items: [
              {
                title: 'Promise状态检查',
                description: '在控制台中检查Promise的状态和值',
                solution: '使用console.log和开发者工具的Promise检查功能',
                prevention: '定期检查Promise的执行状态',
                code: `// Promise调试技巧
const promise = fetch('/api/data');

// 1. 检查Promise状态
console.log('Promise对象:', promise);

// 2. 添加调试信息
promise
  .then(response => {
    console.log('Response received:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Data parsed:', data);
    return data;
  })
  .catch(error => {
    console.error('Error occurred:', error);
    console.trace(); // 打印调用栈
  });

// 3. 使用Promise.allSettled调试多个Promise
const promises = [
  fetch('/api/users'),
  fetch('/api/posts'),
  fetch('/api/comments')
];

Promise.allSettled(promises)
  .then(results => {
    results.forEach((result, index) => {
      console.log(\`Promise \${index}:\`, result);
    });
  });`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
