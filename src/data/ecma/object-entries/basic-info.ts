import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `Object.entries()是ES2017引入的静态方法，用于将对象的可枚举属性转换为键值对数组。它返回一个数组，其中每个元素都是一个包含属性名和属性值的数组[key, value]。这个方法提供了一种标准化的方式来遍历对象的属性，特别适用于需要同时访问键和值的场景。与Object.keys()和Object.values()配合使用，构成了完整的对象属性访问工具集。`,

  syntax: `// 基本语法
Object.entries(obj)

// 参数
obj: 要转换的对象

// 返回值
Array<[string, any]> - 键值对数组

// 使用示例
const obj = { a: 1, b: 2, c: 3 };
const entries = Object.entries(obj);
// [['a', 1], ['b', 2], ['c', 3]]

// 解构使用
for (const [key, value] of Object.entries(obj)) {
  console.log(\`\${key}: \${value}\`);
}

// 与数组方法结合
Object.entries(obj)
  .filter(([key, value]) => value > 1)
  .map(([key, value]) => \`\${key}=\${value}\`)
  .join('&');`,

  quickExample: `// 基础示例
const user = {
  name: 'John',
  age: 30,
  city: 'New York'
};

const entries = Object.entries(user);
console.log(entries);
// [['name', 'John'], ['age', 30], ['city', 'New York']]

// 遍历对象
for (const [key, value] of Object.entries(user)) {
  console.log(\`\${key}: \${value}\`);
}
// name: John
// age: 30
// city: New York

// 转换为查询字符串
const queryString = Object.entries(user)
  .map(([key, value]) => \`\${key}=\${encodeURIComponent(value)}\`)
  .join('&');
console.log(queryString); // name=John&age=30&city=New%20York

// 过滤和转换
const config = {
  debug: true,
  timeout: 5000,
  retries: 3,
  disabled: false
};

const enabledConfig = Object.fromEntries(
  Object.entries(config)
    .filter(([key, value]) => value !== false)
);
console.log(enabledConfig); // { debug: true, timeout: 5000, retries: 3 }

// 对象映射
const prices = { apple: 1.2, banana: 0.8, orange: 1.5 };
const discountedPrices = Object.fromEntries(
  Object.entries(prices)
    .map(([fruit, price]) => [fruit, price * 0.9])
);
console.log(discountedPrices); // { apple: 1.08, banana: 0.72, orange: 1.35 }

// 嵌套对象处理
const data = {
  users: { count: 100, active: 80 },
  posts: { count: 500, published: 450 },
  comments: { count: 1200, approved: 1100 }
};

const summary = Object.entries(data)
  .map(([category, stats]) => ({
    category,
    total: stats.count,
    ratio: (stats.active || stats.published || stats.approved) / stats.count
  }));
console.log(summary);

// 与Map结合使用
const map = new Map(Object.entries(user));
console.log(map.get('name')); // 'John'

// 数组转对象（配合Object.fromEntries）
const pairs = [['x', 1], ['y', 2], ['z', 3]];
const obj = Object.fromEntries(pairs);
console.log(obj); // { x: 1, y: 2, z: 3 }

// 对象属性排序
const unsorted = { c: 3, a: 1, b: 2 };
const sorted = Object.fromEntries(
  Object.entries(unsorted).sort(([a], [b]) => a.localeCompare(b))
);
console.log(sorted); // { a: 1, b: 2, c: 3 }

// 条件对象构建
const formData = { name: 'John', email: '', age: 30, phone: null };
const cleanData = Object.fromEntries(
  Object.entries(formData)
    .filter(([key, value]) => value !== '' && value !== null)
);
console.log(cleanData); // { name: 'John', age: 30 }`,

  coreFeatures: [
    {
      feature: "键值对转换",
      description: "将对象属性转换为[key, value]数组格式",
      importance: "high" as const,
      details: "提供统一的对象遍历接口，便于数据处理"
    },
    {
      feature: "可枚举属性",
      description: "只处理对象的可枚举属性",
      importance: "medium" as const,
      details: "忽略不可枚举属性和Symbol属性"
    },
    {
      feature: "数组方法兼容",
      description: "返回数组，可以使用所有数组方法",
      importance: "high" as const,
      details: "支持map、filter、reduce等函数式编程方法"
    },
    {
      feature: "解构友好",
      description: "支持数组解构和for...of循环",
      importance: "high" as const,
      details: "可以方便地同时获取键和值"
    }
  ],

  keyFeatures: [
    {
      feature: "标准化遍历",
      description: "提供了遍历对象的标准方式",
      importance: "high" as const,
      details: "替代了for...in循环的复杂性"
    },
    {
      feature: "函数式编程",
      description: "支持函数式编程范式",
      importance: "medium" as const,
      details: "与map、filter等方法完美配合"
    },
    {
      feature: "数据转换",
      description: "便于对象和其他数据结构的转换",
      importance: "high" as const,
      details: "与Object.fromEntries()配合实现双向转换"
    }
  ],

  limitations: [
    "只处理可枚举的自有属性",
    "不包括Symbol属性",
    "不包括原型链上的属性",
    "对于大对象可能有性能影响",
    "返回的数组顺序依赖于属性定义顺序"
  ],

  bestPractices: [
    "使用解构语法简化键值对访问",
    "与Object.fromEntries()配合进行对象转换",
    "结合数组方法进行函数式编程",
    "用于对象序列化和反序列化",
    "替代传统的for...in循环"
  ],

  warnings: [
    "只处理可枚举属性，注意属性描述符",
    "大对象遍历可能影响性能",
    "返回的是浅拷贝，修改值会影响原对象"
  ]
};

export default basicInfo;
