import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `ES6模块是JavaScript的标准化模块系统，提供了静态的导入导出机制。模块是独立的文件，具有自己的作用域，可以导出变量、函数、类等，并可以从其他模块导入所需的功能。ES6模块支持静态分析，使得构建工具能够进行树摇优化和代码分割。`,

  syntax: `// 导出语法
export const variable = value;
export function functionName() {}
export class ClassName {}
export { name1, name2 };
export { name1 as alias1 };
export default expression;

// 导入语法
import defaultExport from './module.js';
import { namedExport } from './module.js';
import { name as alias } from './module.js';
import * as namespace from './module.js';
import defaultExport, { namedExport } from './module.js';

// 动态导入
const module = await import('./module.js');

// 重新导出
export { name } from './other-module.js';
export * from './other-module.js';
export { default } from './other-module.js';`,

  quickExample: `// math.js - 数学工具模块
export const PI = 3.14159;
export const E = 2.71828;

export function add(a, b) {
  return a + b;
}

export function multiply(a, b) {
  return a * b;
}

export class Calculator {
  constructor() {
    this.history = [];
  }
  
  calculate(operation, a, b) {
    let result;
    switch (operation) {
      case 'add':
        result = add(a, b);
        break;
      case 'multiply':
        result = multiply(a, b);
        break;
      default:
        throw new Error('Unknown operation');
    }
    
    this.history.push({ operation, a, b, result });
    return result;
  }
  
  getHistory() {
    return this.history;
  }
}

// 默认导出
export default Calculator;

// utils.js - 工具函数模块
export function formatNumber(num, decimals = 2) {
  return num.toFixed(decimals);
}

export function isEven(num) {
  return num % 2 === 0;
}

export const CONSTANTS = {
  MAX_VALUE: 1000000,
  MIN_VALUE: -1000000
};

// app.js - 主应用模块
import Calculator, { PI, E, add, multiply } from './math.js';
import { formatNumber, isEven, CONSTANTS } from './utils.js';

// 使用导入的功能
const calc = new Calculator();

console.log('PI:', PI); // 3.14159
console.log('E:', E); // 2.71828

const sum = add(5, 3);
console.log('5 + 3 =', sum); // 8

const product = multiply(4, 7);
console.log('4 × 7 =', product); // 28

const result = calc.calculate('add', 10, 20);
console.log('Calculator result:', formatNumber(result)); // "30.00"

console.log('Is 4 even?', isEven(4)); // true
console.log('Max value:', CONSTANTS.MAX_VALUE); // 1000000

// 动态导入示例
async function loadModule() {
  try {
    const { default: Calculator, PI } = await import('./math.js');
    const dynamicCalc = new Calculator();
    console.log('Dynamically loaded PI:', PI);
    return dynamicCalc;
  } catch (error) {
    console.error('Failed to load module:', error);
  }
}

loadModule();`,

  coreFeatures: [
    {
      feature: "静态导入导出",
      description: "编译时确定的模块依赖关系",
      importance: "high" as const,
      details: "支持静态分析和树摇优化，提高构建效率"
    },
    {
      feature: "默认导出",
      description: "每个模块可以有一个默认导出",
      importance: "high" as const,
      details: "适合导出模块的主要功能或类"
    },
    {
      feature: "命名导出",
      description: "可以导出多个命名的变量、函数或类",
      importance: "high" as const,
      details: "支持选择性导入和别名"
    },
    {
      feature: "动态导入",
      description: "运行时动态加载模块",
      importance: "medium" as const,
      details: "支持代码分割和按需加载"
    }
  ],

  keyFeatures: [
    {
      feature: "静态结构",
      description: "模块结构在编译时确定，不能动态修改",
      importance: "high" as const,
      details: "使得构建工具能够进行优化"
    },
    {
      feature: "作用域隔离",
      description: "每个模块都有独立的作用域",
      importance: "high" as const,
      details: "避免全局变量污染"
    },
    {
      feature: "循环依赖处理",
      description: "支持模块间的循环依赖",
      importance: "medium" as const,
      details: "通过延迟绑定解决循环引用问题"
    },
    {
      feature: "严格模式",
      description: "模块代码自动运行在严格模式下",
      importance: "medium" as const,
      details: "提高代码质量和安全性"
    }
  ],

  limitations: [
    "模块路径必须是字符串字面量，不能是变量",
    "导入导出语句必须在模块顶层，不能在函数或条件语句中",
    "静态导入不支持条件加载，需要使用动态导入",
    "浏览器支持需要较新版本或构建工具转换",
    "Node.js需要.mjs扩展名或package.json中的type: 'module'"
  ],

  bestPractices: [
    "优先使用命名导出，保持导出名称的一致性",
    "为默认导出使用有意义的名称",
    "避免使用export *，明确指定导出内容",
    "使用动态导入实现代码分割和懒加载",
    "保持模块的单一职责，避免过大的模块文件"
  ],

  warnings: [
    "导入的绑定是只读的，不能重新赋值",
    "模块只会执行一次，多次导入返回同一个实例",
    "循环依赖可能导致未定义的行为，应该避免"
  ]
};

export default basicInfo;
