import React, { useState, use<PERSON>emo, useCallback, useRef, memo, lazy, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Star, Copy, ExternalLink, BookOpen, Settings, Check, Eye } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Drawer, Tabs, Typography, Divider, Alert, List, Button as AntButton, Badge as AntBadge, Space, Card as AntCard } from 'antd';
import { 
  FileTextOutlined, 
  BulbOutlined, 
  CodeOutlined, 
  QuestionCircleOutlined, 
  ExperimentOutlined, 
  HistoryOutlined,
  RocketOutlined,
  BookOutlined,
  RetweetOutlined,
  <PERSON>Outlined,
  ProjectOutlined,
  BugOutlined,
  SettingOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  TrophyOutlined,
  ToolOutlined,
  ClockCircleOutlined,
  CopyOutlined,
  CloseOutlined,
  DownOutlined,
  UpOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  HomeOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CloudOutlined,
  GlobalOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  TeamOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  FastForwardOutlined,
  FastBackwardOutlined,
  StepForwardOutlined,
  StepBackwardOutlined,
  CaretRightOutlined,
  CaretLeftOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  PlusOutlined,
  MinusOutlined,
  CloseCircleOutlined,
  CheckOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ExpandOutlined,
  CompressOutlined,
  MenuOutlined,
  AppstoreOutlined,
  BarsOutlined,
  BorderOutlined,
  DashboardOutlined,
  LineChartOutlined,
  BarChartOutlined,
  PieChartOutlined,
  DotChartOutlined,
  AreaChartOutlined,
  RadarChartOutlined,
  HeatMapOutlined,
  FunnelPlotOutlined,
  StockOutlined,
  BoxPlotOutlined,
  SlackOutlined,
  BellOutlined,
  NotificationOutlined,
  MessageOutlined,
  CommentOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  CompassOutlined,
  AimOutlined,
  ScanOutlined,
  ControlOutlined,
  MonitorOutlined,
  LaptopOutlined,
  BranchesOutlined,
  PullRequestOutlined,
  MergeCellsOutlined,
  SplitCellsOutlined,
  TableOutlined,
  BorderlessTableOutlined,
  RadiusSettingOutlined,
  BorderTopOutlined,
  BorderBottomOutlined,
  BorderLeftOutlined,
  BorderRightOutlined,
  PicCenterOutlined,
  PicLeftOutlined,
  PicRightOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignBottomOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  HighlightOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  BlockOutlined,
  CodeSandboxOutlined,
  ChromeOutlined,
  IeOutlined,
  YoutubeOutlined,
  VideoCameraOutlined,
  PlaySquareOutlined,
  CameraOutlined,
  PictureOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileZipOutlined,
  FileAddOutlined,
  FileDoneOutlined,
  FileExclamationOutlined,
  FileMarkdownOutlined,
  FileProtectOutlined,
  FileSyncOutlined,
  FileUnknownOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FolderAddOutlined,
  FolderViewOutlined,
  InboxOutlined,
  ProfileOutlined,
  SolutionOutlined,
  ScheduleOutlined,
  ReconciliationOutlined,
  AccountBookOutlined,
  WalletOutlined,
  CreditCardOutlined,
  BankOutlined,
  CalculatorOutlined,
  ShoppingCartOutlined,
  ShoppingOutlined,
  GiftOutlined,
  UsergroupAddOutlined
} from "@ant-design/icons";
import { ApiItem, SearchOptions } from "@/types/api";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import MermaidChart from '@/components/MermaidChart';
import CodeHighlight from '@/components/CodeHighlight';
import 'prismjs/themes/prism-tomorrow.css';
import { Dropdown, Menu } from 'antd';
import { Spin } from 'antd';
import { Tag } from 'antd';
import ErrorBoundary from './ErrorBoundary';

// 移除硬编码导入，使用动态数据

const { Title, Paragraph, Text } = Typography;

interface CheatSheetProps {
  title: string;
  subtitle: string;
  apiData: ApiItem[];
  themeColor?: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default';
  onThemeChange?: (theme: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default') => void;
  selectedCategory?: string;
  expandAll?: boolean;
}

// 项目和模式的类型定义
interface ProjectData {
  title: string;
  description: string;
  codeExample: string;
  highlights?: string[];
  useCase?: string;
  technologies?: string[];
  githubLink?: string;
  complexity?: string;
  difficulty?: string;
}

interface PatternData {
  name: string;
  description: string;
  when: string;
  code: string;
}

interface RealWorldData {
  projects?: ProjectData[];
  patterns?: PatternData[];
}

// Tab配置
const TAB_CONFIG = {
  // 核心Tab（始终显示）
  core: [
    { key: 'basic', label: '基本信息', icon: <FileTextOutlined />, description: '语法、参数、返回值' },
    { key: 'business', label: '使用示例', icon: <BulbOutlined />, description: '业务场景和代码示例' },
    { key: 'implementation', label: '原理解析', icon: <CodeOutlined />, description: '底层实现和可视化' },
    { key: 'interview', label: '面试准备', icon: <QuestionCircleOutlined />, description: '高频面试题集' },
    { key: 'questions', label: '常见问题', icon: <ExperimentOutlined />, description: 'FAQ和解决方案' },
    { key: 'archaeology', label: '知识考古', icon: <HistoryOutlined />, description: '历史背景和设计哲学' }
  ],
  // 高级Tab（按需显示）
  advanced: [
    { key: 'performance', label: '性能优化', icon: <RocketOutlined />, description: '最佳实践和性能调优' },
    { key: 'learning', label: '学习路径', icon: <BookOutlined />, description: '渐进式学习指导' },
    { key: 'migration', label: '版本迁移', icon: <RetweetOutlined />, description: '版本升级和迁移指南' },
    { key: 'ecosystem', label: '生态工具', icon: <LinkOutlined />, description: '相关工具和库集成' },
    { key: 'projects', label: '实战项目', icon: <ProjectOutlined />, description: '真实项目案例' },
    { key: 'debugging', label: '调试技巧', icon: <BugOutlined />, description: '问题排查和调试' },
    { key: 'essence', label: '本质洞察', icon: <EyeOutlined />, description: '深层理解和哲学思考' },
    { key: 'extensionTabs', label: '高级特性', icon: <SettingOutlined />, description: '专门特性和深度分析' }
  ]
};

// 推荐Tab配置（基于用户角色）
const RECOMMENDED_TABS = {
  learner: ['basic', 'business', 'learning', 'questions', 'projects'],
  interviewer: ['implementation', 'interview', 'performance', 'migration', 'archaeology'],
  developer: ['basic', 'business', 'performance', 'debugging', 'ecosystem'],
  all: [...TAB_CONFIG.core.map(t => t.key), ...TAB_CONFIG.advanced.map(t => t.key), 'stateManagementComparison', 'thirdPartyIntegration']
};

// 懒加载Tab内容组件
const LazyMarkdownContent = lazy(() => import('./MarkdownContent').catch(() => ({ default: () => <div>内容加载失败</div> })));
const LazyMermaidChart = lazy(() => import('./MermaidChart').catch(() => ({ default: () => <div>图表加载失败</div> })));

// 优化的MarkdownContent组件
const OptimizedMarkdownContent = memo<{ content: string }>(({ content }) => {
  return (
    <Suspense fallback={<div className="animate-pulse bg-gray-200 h-20 rounded"></div>}>
      <LazyMarkdownContent content={content} />
    </Suspense>
  );
});

OptimizedMarkdownContent.displayName = 'OptimizedMarkdownContent';

// 基本信息组件 - 现代化专业设计
const BasicInfoComponent: React.FC<{ item: ApiItem, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const [subTab, setSubTab] = useState('overview');

  const subTabs = [
    { key: 'overview', label: '📋 核心概览' },
    { key: 'syntax', label: '🔧 语法详解' },
    { key: 'patterns', label: '🎯 使用模式' },
    { key: 'comparison', label: '⚖️ 对比分析' }
  ];

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">📖</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">基本信息</Title>
        <span className="text-slate-500 text-sm">核心概念与基础语法</span>
      </div>

      {/* 响应式Tab导航 */}
      <div className="bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50 p-1.5">
        {/* 移动端：滚动式Tab */}
        <div className="md:hidden">
          <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
            {subTabs.map(tab => (
              <button
                key={tab.key}
                onClick={() => setSubTab(tab.key)}
                className={`relative flex-shrink-0 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 whitespace-nowrap ${
                  subTab === tab.key
                    ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <span className="text-sm font-medium">{tab.label}</span>
                {subTab === tab.key && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* 桌面端：网格式Tab */}
        <div className="hidden md:flex gap-2">
          {subTabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setSubTab(tab.key)}
              className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
                subTab === tab.key
                  ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }`}
            >
              <span className="text-sm font-medium">{tab.label}</span>
              {subTab === tab.key && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full"></div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Tab内容区域 */}
      <div className="min-h-[300px] space-y-4">
        {subTab === 'overview' && (
          <div className="space-y-6">
            {/* 核心定义 - 简化设计 */}
            <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200/50 rounded-xl p-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-amber-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-lg">📋</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-amber-900 mb-3">Promise 核心概念</h3>
                  <p className="text-amber-800 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </div>
            </div>

            {/* 语法速览 - 次要信息，简化处理 */}
            <div className="bg-white/60 border border-slate-200/30 rounded-lg p-4 hover:bg-white hover:border-slate-200 transition-all duration-300">
              <details className="group">
                <summary className="flex items-center gap-3 cursor-pointer list-none">
                  <div className="w-8 h-8 bg-slate-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">⚡</span>
                  </div>
                  <span className="text-slate-700 font-medium">语法速览</span>
                  <div className="ml-auto w-5 h-5 text-slate-400 group-open:rotate-180 transition-transform">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </summary>
                <div className="mt-4 bg-slate-50 p-4 rounded-lg border border-slate-200/50">
                  <pre className="text-slate-700 font-mono text-sm leading-relaxed overflow-x-auto">
                    <code>{item.syntax}</code>
                  </pre>
                </div>
              </details>
            </div>

            {/* 核心特性 - 改进交互和视觉层次 */}
            {item.basicInfo?.keyFeatures && (
              <div className="bg-white border border-slate-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-emerald-500 rounded-xl flex items-center justify-center">
                      <span className="text-white text-lg">⭐</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900">核心特性</h3>
                      <p className="text-slate-500 text-sm">Promise的关键能力</p>
                    </div>
                  </div>
                  <div className="text-xs text-slate-400 bg-slate-100 px-2 py-1 rounded-full">
                    {item.basicInfo.keyFeatures.length} 项特性
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {item.basicInfo.keyFeatures.slice(0, 4).map((feature, index) => (
                    <div
                      key={index}
                      className="group p-4 bg-emerald-50 hover:bg-emerald-100 rounded-xl border border-emerald-200/50 hover:border-emerald-300 transition-all duration-300 cursor-pointer hover:shadow-sm"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-white text-xs font-bold">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-emerald-900 font-semibold text-sm mb-2 group-hover:text-emerald-800 transition-colors">
                            {feature.feature}
                          </h4>
                          <p className="text-emerald-700 text-xs leading-relaxed">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {item.basicInfo.keyFeatures.length > 4 && (
                  <div className="mt-4 text-center">
                    <button className="text-emerald-600 hover:text-emerald-700 text-sm font-medium hover:underline transition-colors">
                      查看全部 {item.basicInfo.keyFeatures.length} 项特性 →
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* 快速示例 - 支持新旧版本和Mermaid图 */}
            <div className="bg-white border border-slate-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">💻</span>
                </div>
                <Title level={5} className="mb-0 text-slate-900 font-medium text-base">快速示例</Title>
              </div>

              {/* 优先使用新版本的quickExamples */}
              {item.basicInfo?.quickExamples ? (
                <div className="space-y-6">
                  {item.basicInfo.quickExamples.map((example, index) => (
                    <div key={index} className="space-y-4">
                      <div className="border-l-4 border-purple-500 pl-4">
                        <h6 className="text-slate-900 font-medium text-sm mb-1">{example.title}</h6>
                        <p className="text-slate-600 text-xs leading-relaxed">{example.description}</p>
                      </div>
                      <CodeHighlight
                        code={example.code}
                        language="javascript"
                        title={example.title}
                        onCopy={() => copyToClipboard(example.code)}
                      />
                      {example.diagram && (
                        <div className="bg-purple-50 p-4 rounded-md border border-purple-200/50">
                          <MermaidChart chart={example.diagram} title={`${example.title} - 流程图`} />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                /* 兼容旧版本 */
                <div className="space-y-4">
                  <CodeHighlight
                    code={item.basicInfo?.quickExampleCode || item.basicInfo?.quickExample || item.example}
                    language="javascript"
                    title="基本用法"
                    onCopy={() => copyToClipboard(item.basicInfo?.quickExampleCode || item.basicInfo?.quickExample || item.example)}
                  />
                  {item.basicInfo?.quickExampleDiagram && (
                    <div className="bg-purple-50 p-4 rounded-md border border-purple-200/50">
                      <MermaidChart chart={item.basicInfo.quickExampleDiagram} title="快速示例流程图" />
                    </div>
                  )}
                  {item.basicInfo?.parallelExampleCode && (
                    <div className="space-y-4">
                      <div className="border-l-4 border-green-500 pl-4">
                        <h6 className="text-slate-900 font-medium text-sm mb-1">并行处理示例</h6>
                        <p className="text-slate-600 text-xs leading-relaxed">展示并行异步操作的处理方式</p>
                      </div>
                      <CodeHighlight
                        code={item.basicInfo.parallelExampleCode}
                        language="javascript"
                        title="并行处理"
                        onCopy={() => copyToClipboard(item.basicInfo.parallelExampleCode)}
                      />
                      {item.basicInfo?.parallelExampleDiagram && (
                        <div className="bg-green-50 p-4 rounded-md border border-green-200/50">
                          <MermaidChart chart={item.basicInfo.parallelExampleDiagram} title="并行处理流程图" />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 业务场景图表 - 支持新旧版本 */}
            {(item.basicInfo?.scenarioDiagrams || item.basicInfo?.scenarioDiagram) && (
              <div className="bg-white border border-slate-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-cyan-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-xs">🗺️</span>
                  </div>
                  <Title level={5} className="mb-0 text-slate-900 font-medium text-base">业务场景图</Title>
                </div>

                {/* 优先使用新版本的scenarioDiagrams */}
                {item.basicInfo?.scenarioDiagrams ? (
                  <div className="space-y-6">
                    {item.basicInfo.scenarioDiagrams.map((diagram, index) => (
                      <div key={index} className="bg-cyan-50 p-4 rounded-md border border-cyan-200/50">
                        <div className="mb-3">
                          <h6 className="text-slate-900 font-medium text-sm mb-1">{diagram.title}</h6>
                          {diagram.description && (
                            <p className="text-slate-600 text-xs leading-relaxed">{diagram.description}</p>
                          )}
                        </div>
                        <MermaidChart chart={diagram.diagram} title={diagram.title} />
                      </div>
                    ))}
                  </div>
                ) : item.basicInfo?.scenarioDiagram ? (
                  /* 兼容旧版本 */
                  typeof item.basicInfo.scenarioDiagram === 'string' ? (
                    <div className="bg-cyan-50 p-4 rounded-md border border-cyan-200/50">
                      <MermaidChart chart={item.basicInfo.scenarioDiagram} title="业务场景图" />
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {item.basicInfo.scenarioDiagram.map((diagram: any, index: number) => (
                        <div key={index} className="bg-cyan-50 p-4 rounded-md border border-cyan-200/50">
                          <div className="mb-3">
                            <h6 className="text-slate-900 font-medium text-sm mb-1">{diagram.title}</h6>
                            <p className="text-slate-600 text-xs leading-relaxed">{diagram.description}</p>
                          </div>
                          <MermaidChart chart={diagram.diagram} title={diagram.title} />
                        </div>
                      ))}
                    </div>
                  )
                ) : null}
              </div>
            )}

            {/* 常见使用案例 */}
            {item.basicInfo.commonUseCases && item.basicInfo.commonUseCases.length > 0 && (
              <div className="mt-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-green-600 rounded-md flex items-center justify-center">
                    <span className="text-white text-xs">💡</span>
                  </div>
                  <Title level={5} className="mb-0 text-green-900 font-medium text-base">常见使用案例</Title>
                </div>

                <div className="space-y-6">
                  {item.basicInfo.commonUseCases.map((useCase, index) => (
                    <div key={index} className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-start gap-3 mb-3">
                        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <span className="text-white text-sm font-bold">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h6 className="text-lg font-semibold text-green-900 mb-2">{useCase.title}</h6>
                          <p className="text-green-800 mb-3">{useCase.description}</p>

                          {/* 难度和解释 */}
                          <div className="flex items-center gap-4 mb-3">
                            {useCase.difficulty && (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                useCase.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                                useCase.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {useCase.difficulty === 'beginner' ? '初级' :
                                 useCase.difficulty === 'intermediate' ? '中级' : '高级'}
                              </span>
                            )}
                            {useCase.explanation && (
                              <span className="text-green-700 text-sm italic">{useCase.explanation}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* 代码示例 */}
                      {useCase.code && (
                        <div className="mb-4">
                          <CodeHighlight
                            code={useCase.code}
                            language="javascript"
                            title={`${useCase.title} - 代码示例`}
                            onCopy={() => copyToClipboard(useCase.code)}
                          />
                        </div>
                      )}

                      {/* Mermaid图表 */}
                      {useCase.diagram && (
                        <div className="bg-white/70 p-4 rounded-lg border border-green-300/50">
                          <h6 className="text-md font-semibold text-green-800 mb-3">📊 流程图解</h6>
                          <div className="bg-white p-4 rounded-md border border-green-300/30">
                            <MermaidChart
                              chart={useCase.diagram}
                              title={`${useCase.title}流程图`}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {subTab === 'syntax' && (
          <div className="bg-white border border-slate-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-slate-600 rounded-md flex items-center justify-center">
                <span className="text-white text-xs">🔧</span>
              </div>
              <Title level={5} className="mb-0 text-slate-900 font-medium text-base">语法详解</Title>
            </div>

            <div className="space-y-4">
              <div>
                <Text strong className="text-slate-800 block mb-2 font-medium text-sm">完整语法</Text>
                <CodeHighlight
                  code={item.basicInfo?.syntax || item.syntax}
                  language="javascript"
                  title="语法详解"
                  onCopy={() => copyToClipboard(item.basicInfo?.syntax || item.syntax)}
                />
              </div>

              <div>
                <Text strong className="text-slate-800 block mb-2 font-medium text-sm">完整示例</Text>
                <CodeHighlight
                  code={item.example}
                  language="javascript"
                  title="详细用法"
                  onCopy={() => copyToClipboard(item.example)}
                />
              </div>
            </div>
          </div>
        )}

        {subTab === 'patterns' && (
          <div className="bg-white border border-slate-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-indigo-500 rounded-md flex items-center justify-center">
                <span className="text-white text-xs">🎯</span>
              </div>
              <Title level={5} className="mb-0 text-slate-900 font-medium text-base">使用模式</Title>
            </div>

            <div className="space-y-6">
              {/* 最佳实践列表 */}
              {item.basicInfo?.bestPractices && item.basicInfo.bestPractices.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-6 h-6 bg-indigo-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-xs">📋</span>
                    </div>
                    <Text strong className="text-slate-800 font-medium text-base">核心实践精选</Text>
                  </div>

                  {/* 精选核心实践 */}
                  <div className="space-y-4">
                    {(() => {
                      // 解析最佳实践，按类别分组
                      const categories = new Map();
                      let currentCategory = '核心实践';

                      item.basicInfo.bestPractices.forEach((practice, index) => {
                        const practiceText = typeof practice === 'string' ? practice : practice?.practice || '';

                        // 跳过注释行（以//开头或只包含===的行）
                        if (practiceText.trim().startsWith('//') || practiceText.trim().match(/^===.*===$/) || practiceText.trim() === '') {
                          return;
                        }

                        // 检测类别标题（包含【】的内容）
                        const categoryMatch = practiceText.match(/【(.+?)】/);
                        if (categoryMatch) {
                          const categoryName = categoryMatch[1];
                          // 提取类别名称，去掉emoji等
                          const cleanCategory = categoryName.replace(/🎯|🚀|📝|🔄|🛠️|🧪|🏗️|🔒|🌐/g, '').trim();
                          currentCategory = cleanCategory || '核心实践';

                          // 如果这行只是类别标题，不添加到实践中
                          if (practiceText.trim() === `【${categoryName}】`) {
                            return;
                          }
                        }

                        if (!categories.has(currentCategory)) {
                          categories.set(currentCategory, []);
                        }

                        // 清理实践文本，移除【】标记
                        const cleanText = practiceText.replace(/【[^】]+】/, '').trim();
                        if (cleanText) {
                          categories.get(currentCategory).push({ text: cleanText, index: categories.get(currentCategory).length + 1 });
                        }
                      });

                      // 只显示前3个最重要的类别，每个类别最多5条
                      const topCategories = Array.from(categories.entries()).slice(0, 3);

                      return topCategories.map(([category, practices]) => (
                        <div key={category} className="bg-white rounded-lg border border-slate-200/50 overflow-hidden shadow-sm">
                          <div className="bg-gradient-to-r from-indigo-50 to-blue-50 px-4 py-3 border-b border-slate-200/50">
                            <h6 className="text-indigo-800 font-semibold text-sm flex items-center gap-2">
                              <span className="w-2 h-2 bg-indigo-500 rounded-full"></span>
                              {category}
                            </h6>
                          </div>
                          <div className="p-4 space-y-3">
                            {practices.slice(0, 5).map(({ text, index }) => (
                              <div key={index} className="flex items-start gap-3 p-3 bg-slate-50 rounded-md hover:bg-slate-100 transition-colors">
                                <div className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <span className="text-indigo-600 text-xs font-bold">{index}</span>
                                </div>
                                <Text className="text-slate-700 text-sm leading-relaxed flex-1">
                                  {text}
                                </Text>
                              </div>
                            ))}
                            {practices.length > 5 && (
                              <div className="text-center pt-2">
                                <Text className="text-slate-500 text-xs">还有 {practices.length - 5} 条实践...</Text>
                              </div>
                            )}
                          </div>
                        </div>
                      ));
                    })()}
                  </div>
                </div>
              ) : (
                <div className="p-4 bg-indigo-50 rounded-lg border border-indigo-200/50">
                  <Text strong className="text-indigo-800 block mb-2 font-medium text-sm">✅ 推荐用法</Text>
                  <Text className="text-indigo-700 text-sm leading-relaxed">
                    根据API特性动态展示推荐用法
                  </Text>
                </div>
              )}

              {/* 注意事项和避免场景 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-amber-50 rounded-lg border border-amber-200/50">
                  <Text strong className="text-amber-800 block mb-2 font-medium text-sm">⚠️ 注意事项</Text>
                  <Text className="text-amber-700 text-sm leading-relaxed">
                    {item.notes || item.basicInfo?.warnings?.[0] || '根据API特性动态展示注意事项'}
                  </Text>
                </div>

                <div className="p-4 bg-red-50 rounded-lg border border-red-200/50">
                  <Text strong className="text-red-800 block mb-2 font-medium text-sm">❌ 避免场景</Text>
                  <Text className="text-red-700 text-sm leading-relaxed">
                    {item.basicInfo?.limitations?.[0] || '根据API特性动态展示避免场景'}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}

        {subTab === 'comparison' && (
          <div className="bg-white border border-slate-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 bg-orange-500 rounded-md flex items-center justify-center">
                <span className="text-white text-xs">⚖️</span>
              </div>
              <Title level={5} className="mb-0 text-slate-900 font-medium text-base">对比分析</Title>
            </div>

            {item.basicInfo?.comparisonAnalysis ? (
              <div className="space-y-6">
                {/* 对比分析标题和描述 */}
                <div className="mb-4">
                  <Title level={5} className="text-slate-900 mb-2">
                    {item.basicInfo.comparisonAnalysis.title}
                  </Title>
                  <Text className="text-slate-600">
                    {item.basicInfo.comparisonAnalysis.description}
                  </Text>
                </div>

                {/* 对比表格 */}
                <div className="space-y-4">
                  {item.basicInfo.comparisonAnalysis.comparisons.map((comparison: { 
                    name: string; 
                    description: string; 
                    advantages: string[]; 
                    disadvantages: string[]; 
                    useCases: string[]; 
                    performance: string; 
                    complexity: string; 
                  }, index: number) => (
                    <div key={index} className="border border-slate-200 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className={"w-3 h-3 rounded-full " + (
                          index === 0 ? 'bg-blue-500' : 
                          index === 1 ? 'bg-green-500' : 
                          index === 2 ? 'bg-purple-500' : 'bg-orange-500'
                        )}></div>
                        <Title level={5} className="text-slate-900 mb-0">
                          {comparison.name}
                        </Title>
                        <Tag color="default">{comparison.complexity}</Tag>
                      </div>
                      
                      <Text className="text-slate-600 text-sm mb-3">
                        {comparison.description}
                      </Text>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* 优势 */}
                        <div className="bg-green-50 p-3 rounded-md border border-green-200/50">
                          <Text strong className="text-green-800 block mb-2">✅ 优势</Text>
                          <div className="space-y-1">
                            {comparison.advantages.map((advantage: string, idx: number) => (
                              <div key={idx} className="flex items-start gap-2">
                                <span className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2 flex-shrink-0"></span>
                                <Text className="text-green-700 text-sm">{advantage}</Text>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* 劣势 */}
                        <div className="bg-red-50 p-3 rounded-md border border-red-200/50">
                          <Text strong className="text-red-800 block mb-2">❌ 劣势</Text>
                          <div className="space-y-1">
                            {comparison.disadvantages.map((disadvantage: string, idx: number) => (
                              <div key={idx} className="flex items-start gap-2">
                                <span className="w-1.5 h-1.5 bg-red-600 rounded-full mt-2 flex-shrink-0"></span>
                                <Text className="text-red-700 text-sm">{disadvantage}</Text>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="mt-3 flex flex-wrap gap-2">
                        <Text strong className="text-slate-700 text-sm">适用场景: </Text>
                        {comparison.useCases.map((useCase: string, idx: number) => (
                          <Tag key={idx} color="blue">{useCase}</Tag>
                        ))}
                      </div>

                      <div className="mt-2">
                        <Text className="text-slate-600 text-sm">
                          <strong>性能表现:</strong> {comparison.performance}
                        </Text>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 决策矩阵 */}
                {item.basicInfo.comparisonAnalysis.decisionMatrix && (
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200/50">
                    <Title level={5} className="text-blue-900 mb-2">
                      📊 选择决策矩阵
                    </Title>
                    <Text className="text-blue-700 text-sm mb-3">
                      {item.basicInfo.comparisonAnalysis.decisionMatrix.description}
                    </Text>
                    <div className="space-y-2">
                      {item.basicInfo.comparisonAnalysis.decisionMatrix.scenarios.map((scenario: { 
                        scenario: string; 
                        recommended: string; 
                        reason: string; 
                      }, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-md">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <Text strong className="text-slate-900 text-sm">
                              {scenario.scenario} → {scenario.recommended}
                            </Text>
                            <Text className="text-slate-600 text-xs block mt-1">
                              {scenario.reason}
                            </Text>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 总结 */}
                <div className="bg-slate-50 p-4 rounded-md border border-slate-200/50">
                  <Text strong className="text-slate-800 block mb-2">📝 总结</Text>
                  <Text className="text-slate-700 text-sm leading-relaxed">
                    {item.basicInfo.comparisonAnalysis.summary}
                  </Text>
                </div>
              </div>
            ) : (
              <div className="bg-slate-50 p-4 rounded-md border border-slate-200/50">
                <Text className="text-slate-700 text-sm leading-relaxed">
                  对比分析内容将根据具体API动态生成，展示与相关API的差异和优势。
                </Text>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// 渲染函数定义
const renderBasicInfo = (item: ApiItem, copyToClipboard: (text: string) => void) => (
  <BasicInfoComponent item={item} copyToClipboard={copyToClipboard} />
);



// 使用示例组件 - 现代化专业设计
const BusinessScenariosComponent: React.FC<{ item: ApiItem, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const [subTab, setSubTab] = useState(0);
  const scenarios = item.businessScenarios || [];

  if (!scenarios || scenarios.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">💼</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无业务场景</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-xs">💼</span>
          </div>
          <Title level={4} className="!mb-0 text-slate-900 font-medium">使用示例</Title>
          <span className="text-slate-500 text-sm">业务场景与代码实现</span>
        </div>
        <div className="px-3 py-1.5 bg-slate-100 rounded-full">
          <span className="text-slate-700 text-sm font-medium">{scenarios.length} 个场景</span>
        </div>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {scenarios.map((scenario, index) => (
          <button
            key={index}
            onClick={() => setSubTab(index)}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              subTab === index
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <div className="flex items-center gap-2">
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                subTab === index
                  ? 'bg-slate-600 text-white'
                  : 'bg-slate-300 text-slate-600'
              }`}>
                {index + 1}
              </span>
              <span className="text-sm font-medium truncate">{scenario.title}</span>
            </div>
            {subTab === index && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* 当前场景内容 */}
      {scenarios[subTab] && (
        <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
          <div className="space-y-6">
            {/* 场景标题 */}
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-gray-700 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold">{subTab + 1}</span>
              </div>
              <div>
                <Title level={4} className="mb-1 text-slate-900">{scenarios[subTab].title}</Title>
                <Text className="text-slate-600">{scenarios[subTab].description}</Text>
              </div>
            </div>

            {/* 业务价值 */}
            <div className="bg-emerald-50 p-4 rounded-xl border border-emerald-200/50">
              <Text strong className="text-emerald-800 block mb-2">💰 业务价值</Text>
              <Text className="text-emerald-700">{scenarios[subTab].businessValue}</Text>
            </div>

            {/* 应用场景 */}
            <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
              <Text strong className="text-blue-800 block mb-2">🎯 应用场景</Text>
              <Text className="text-blue-700">{scenarios[subTab].scenario}</Text>
            </div>

            {/* 代码示例 */}
            <div>
              <Text strong className="text-slate-800 block mb-3">💻 代码实现</Text>
              <CodeHighlight
                code={scenarios[subTab].code}
                language="javascript"
                title={scenarios[subTab].title}
                onCopy={() => copyToClipboard(scenarios[subTab].code)}
              />
            </div>

            {/* 架构图表 */}
            {scenarios[subTab].diagrams && scenarios[subTab].diagrams.length > 0 && (
              <div className="bg-cyan-50 p-4 rounded-xl border border-cyan-200/50">
                <Text strong className="text-cyan-800 block mb-3">📊 架构图表</Text>
                <div className="space-y-6">
                  {scenarios[subTab].diagrams.map((diagram, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-cyan-300/30">
                      <div className="mb-3">
                        <h6 className="text-lg font-semibold text-cyan-900 mb-2">{diagram.title}</h6>
                        {diagram.description && (
                          <p className="text-cyan-800 text-sm leading-relaxed">{diagram.description}</p>
                        )}
                      </div>
                      <div className="bg-white rounded-md border border-cyan-300/50 overflow-hidden">
                        <MermaidChart
                          chart={diagram.diagram}
                          title={diagram.title}
                          id={`business-diagram-${subTab}-${index}`}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 技术解析 */}
            {scenarios[subTab].explanation && (
              <div className="bg-amber-50 p-4 rounded-xl border border-amber-200/50">
                <Text strong className="text-amber-800 block mb-3">🔍 技术解析</Text>
                <div className="prose prose-slate max-w-none">
                  <OptimizedMarkdownContent content={scenarios[subTab].explanation} />
                </div>
              </div>
            )}

            {/* 核心优势 */}
            {scenarios[subTab].benefits && (
              <div className="bg-purple-50 p-4 rounded-xl border border-purple-200/50">
                <Text strong className="text-purple-800 block mb-3">✨ 核心优势</Text>
                <div className="space-y-2">
                  {scenarios[subTab].benefits.map((benefit, idx) => (
                    <div key={idx} className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                      <Text className="text-purple-700">{benefit}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const renderBusinessScenarios = (item: ApiItem, copyToClipboard: (text: string) => void) => (
  <BusinessScenariosComponent item={item} copyToClipboard={copyToClipboard} />
);

// 实现原理组件 - 现代化专业设计
const ImplementationComponent: React.FC<{ item: ApiItem }> = ({ item }) => {
  const [subTab, setSubTab] = useState('overview');

  if (!item.implementation) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🔬</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无实现原理内容</Text>
        </div>
      </div>
    );
  }

  const subTabs = [
    { key: 'overview', label: '📋 原理概览' },
    { key: 'mechanism', label: '⚙️ 核心机制' },
    { key: 'visualization', label: '📊 可视化图解' },
    { key: 'explanation', label: '💡 通俗解释' },
    { key: 'design', label: '🎯 设计考量' }
  ];

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🔬</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">底层实现原理</Title>
        <span className="text-slate-500 text-sm">深入理解架构设计与核心机制</span>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {subTabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setSubTab(tab.key)}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              subTab === tab.key
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="text-sm font-medium">{tab.label}</span>
            {subTab === tab.key && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* Tab内容区域 */}
      <div className="min-h-[400px]">
        {subTab === 'overview' && (
          <div className="space-y-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl"></div>
              <div className="relative p-5 border border-blue-200/50 rounded-xl">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">📖</span>
                  </div>
                  <Text strong className="text-blue-700 font-semibold">快速了解</Text>
                </div>
                <Text className="text-blue-600 text-sm">这个Tab提供核心概念的快速概览，帮助您在1分钟内掌握要点</Text>
              </div>
            </div>
            <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
              <div className="prose prose-slate max-w-none">
                <OptimizedMarkdownContent content={
                  item.implementation.plainExplanation ||
                  // 如果没有简洁说明，从mechanism中提取前几段作为概览
                  (item.implementation.mechanism ?
                    item.implementation.mechanism.split('\n\n').slice(0, 2).join('\n\n') + '\n\n*查看"核心机制"tab了解详细实现...*' :
                    '暂无概览内容'
                  )
                } />
              </div>
            </div>
          </div>
        )}

        {subTab === 'mechanism' && (
          <div className="space-y-6">
            <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">⚙️</span>
                </div>
                <Title level={5} className="mb-0 text-slate-800 font-semibold">核心实现机制</Title>
              </div>
              <div className="prose prose-slate max-w-none">
                <OptimizedMarkdownContent content={item.implementation.mechanism || '暂无机制说明'} />
              </div>
            </div>
          </div>
        )}

        {subTab === 'visualization' && (
          <div className="space-y-6">
            <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">📊</span>
                </div>
                <Title level={5} className="mb-0 text-slate-800 font-semibold">架构可视化</Title>
              </div>
              {item.implementation.visualization ? (
                <MermaidChart chart={item.implementation.visualization} title="架构可视化图解" />
              ) : (
                <div className="relative mt-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl"></div>
                  <div className="relative p-4 border border-amber-200/50 rounded-xl">
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">⚠</span>
                      </div>
                      <Text className="text-amber-700 font-medium">暂无可视化图表</Text>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {subTab === 'explanation' && (
          <div className="space-y-6">
            <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6 border-l-4 border-amber-400">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-amber-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">💡</span>
                </div>
                <Title level={5} className="mb-0 text-slate-800 font-semibold">通俗解释</Title>
              </div>
              <div className="prose prose-slate max-w-none">
                <OptimizedMarkdownContent content={item.implementation.plainExplanation || '暂无通俗解释'} />
              </div>
            </div>
          </div>
        )}

        {subTab === 'design' && (
          <div className="space-y-8">
            {/* 设计考量主要内容 */}
            {item.implementation.designConsiderations && (
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white text-sm">🎯</span>
                  </div>
                  <div>
                    <Title level={4} className="mb-0 text-slate-900 font-bold">核心设计考量</Title>
                    <Text className="text-slate-600 text-sm">深入理解架构设计背后的思考与权衡</Text>
                  </div>
                </div>

                <div className="grid gap-6">
                  {item.implementation.designConsiderations.map((consideration, index) => {
                    // 解析设计考量，分离标题和内容
                    const parts = consideration.split(' - ');
                    const title = parts[0] || `设计考量 ${index + 1}`;
                    const description = parts[1] || consideration;

                    return (
                      <div key={index} className="group relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl transform group-hover:scale-[1.02] transition-transform duration-300"></div>
                        <div className="relative bg-white/80 backdrop-blur-sm border border-emerald-200/50 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300">
                          <div className="flex items-start gap-4">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center border border-emerald-200/50">
                                <span className="text-emerald-600 font-bold text-lg">{index + 1}</span>
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="text-emerald-800 font-bold text-lg mb-3 leading-tight">{title}</h5>
                              <div className="prose prose-slate prose-sm max-w-none">
                                <Text className="text-slate-700 leading-relaxed">{description}</Text>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* 相关概念区域 */}
            {item.implementation.relatedConcepts && (
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-slate-50 to-gray-50 rounded-2xl"></div>
                <div className="relative bg-white/90 backdrop-blur-sm border border-slate-200/50 rounded-2xl p-6 shadow-sm">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-7 h-7 bg-gradient-to-r from-slate-500 to-gray-600 rounded-lg flex items-center justify-center shadow-md">
                      <span className="text-white text-sm">🔗</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-0 text-slate-800 font-bold">相关技术概念</Title>
                      <Text className="text-slate-600 text-xs">扩展学习和深入理解</Text>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {item.implementation.relatedConcepts.map((concept, index) => {
                      // 解析概念，分离名称和描述
                      const parts = concept.split('：');
                      const name = parts[0] || concept;
                      const desc = parts[1] || '';

                      return (
                        <div key={index} className="group relative">
                          <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-4 border border-slate-200/50 hover:border-slate-300/50 transition-all duration-300 hover:shadow-sm">
                            <div className="flex items-start gap-3">
                              <div className="w-6 h-6 bg-slate-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span className="text-slate-600 text-xs font-bold">{index + 1}</span>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h6 className="text-slate-800 font-semibold text-sm mb-1 leading-tight">{name}</h6>
                                {desc && (
                                  <Text className="text-slate-600 text-xs leading-relaxed">{desc}</Text>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const renderInterviewQuestions = (item: ApiItem, copyToClipboard: (text: string) => void, secondaryTabStates: Record<string, number>, setSecondaryTabStates: React.Dispatch<React.SetStateAction<Record<string, number>>>) => {
  const currentQuestionIndex = secondaryTabStates.interviewQuestions || 0;
  
  if (!item.interviewQuestions || item.interviewQuestions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🎯</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无面试题</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-xs">🎯</span>
          </div>
          <Title level={4} className="!mb-0 text-slate-900 font-medium">面试准备</Title>
          <span className="text-slate-500 text-sm">高频面试题与标准答案</span>
        </div>
        <div className="px-3 py-1.5 bg-slate-100 rounded-full">
          <span className="text-slate-700 text-sm font-medium">{item.interviewQuestions.length} 道题目</span>
        </div>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {item.interviewQuestions.map((_, index) => (
          <button
            key={index}
            onClick={() => setSecondaryTabStates(prev => ({
              ...prev,
              interviewQuestions: index
            }))}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              currentQuestionIndex === index
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <div className="flex items-center gap-2">
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                currentQuestionIndex === index
                  ? 'bg-slate-600 text-white'
                  : 'bg-slate-300 text-slate-600'
              }`}>
                {index + 1}
              </span>
              <span className="text-sm font-medium">题目 {index + 1}</span>
            </div>
            {currentQuestionIndex === index && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* 当前题目内容 */}
      {item.interviewQuestions[currentQuestionIndex] && (
        <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
          <div className="space-y-6">
            {/* 问题 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl"></div>
              <div className="relative p-5 border border-blue-200/50 rounded-xl border-l-4 border-blue-400">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">🤔</span>
                  </div>
                  <Text strong className="text-blue-700 font-semibold">问题</Text>
                </div>
                <Text className="text-slate-800 text-base leading-relaxed">
                  {item.interviewQuestions[currentQuestionIndex].question}
                </Text>
              </div>
            </div>

            {/* 答案 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl"></div>
              <div className="relative p-5 border border-emerald-200/50 rounded-xl border-l-4 border-emerald-400">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">✅</span>
                  </div>
                  <Text strong className="text-emerald-700 font-semibold">参考答案</Text>
                </div>

                <div className="space-y-4">
                  {/* 简要答案 */}
                  {item.interviewQuestions[currentQuestionIndex].answer.brief && (
                    <div className="bg-emerald-100/80 p-4 rounded-lg border border-emerald-200/50">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-5 h-5 bg-emerald-200 rounded-lg flex items-center justify-center">
                          <span className="text-emerald-700 text-xs">📋</span>
                        </div>
                        <Text strong className="text-emerald-800 font-semibold">简要回答</Text>
                      </div>
                      <Text className="text-emerald-800 leading-relaxed">
                        {item.interviewQuestions[currentQuestionIndex].answer.brief}
                      </Text>
                    </div>
                  )}

                  {/* 详细答案 */}
                  {item.interviewQuestions[currentQuestionIndex].answer.detailed && (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-5 h-5 bg-emerald-200 rounded-lg flex items-center justify-center">
                          <span className="text-emerald-700 text-xs">📚</span>
                        </div>
                        <Text strong className="text-emerald-800 font-semibold">详细解析</Text>
                      </div>
                      <div className="prose prose-slate max-w-none">
                        <OptimizedMarkdownContent content={item.interviewQuestions[currentQuestionIndex].answer.detailed} />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 代码示例 */}
            {item.interviewQuestions[currentQuestionIndex].answer.code && (
              <div className="relative border border-slate-200/50 rounded-xl bg-white/90 backdrop-blur-sm p-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">💻</span>
                  </div>
                  <Text strong className="text-slate-800 font-semibold">代码示例</Text>
                </div>
                <CodeHighlight
                  code={item.interviewQuestions[currentQuestionIndex].answer.code || ''}
                  language="javascript"
                  title="相关代码"
                  onCopy={() => copyToClipboard(item.interviewQuestions[currentQuestionIndex].answer.code || '')}
                />
              </div>
            )}

            {/* 可视化图表 */}
            {item.interviewQuestions[currentQuestionIndex].visualization && (
              <div className="relative border border-slate-200/50 rounded-xl bg-white/90 backdrop-blur-sm p-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">📊</span>
                  </div>
                  <Text strong className="text-slate-800 font-semibold">可视化说明</Text>
                </div>
                <div className="bg-white p-6 rounded-xl border border-slate-200/50">
                  <MermaidChart 
                    chart={item.interviewQuestions[currentQuestionIndex].visualization} 
                    title={`题目${currentQuestionIndex + 1}可视化说明`}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 知识考古组件
const KnowledgeArchaeologyComponent: React.FC<{ item: ApiItem }> = ({ item }) => {
  const [subTab, setSubTab] = useState('timeline');
  
  if (!item.knowledgeArchaeology) {
    return <Paragraph>暂无知识考古内容</Paragraph>;
  }

  const subTabs = [
    { key: 'timeline', label: '📅 发展时间线' },
    { key: 'background', label: '🌟 历史背景' },
    { key: 'figures', label: '👥 关键人物' },
    { key: 'concepts', label: '💡 核心概念' }
  ];

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🏛️</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">知识考古</Title>
        <span className="text-slate-500 text-sm">技术历史与演进脉络</span>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {subTabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setSubTab(tab.key)}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              subTab === tab.key
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="text-sm font-medium">{tab.label}</span>
            {subTab === tab.key && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* Tab内容区域 */}
      <div className="min-h-[400px]">
        {subTab === 'timeline' && item.knowledgeArchaeology.timeline && (
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-400 to-purple-400"></div>
              {item.knowledgeArchaeology.timeline.map((event, index) => (
                <div key={index} className="relative pl-12 pb-8">
                  <div className="absolute left-2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow"></div>
                  <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center gap-4 mb-3">
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                        {event.year}
                      </span>
                      <Title level={5} className="text-gray-800 mb-0">
                        {event.event}
                      </Title>
                    </div>
                    <Paragraph className="text-gray-600 mb-3">
                      {event.description}
                    </Paragraph>
                    <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                      <Text className="text-blue-700 text-sm">
                        💡 意义：{event.significance}
                      </Text>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {subTab === 'background' && (
          <div className="space-y-6">
            <div className="bg-amber-50 p-6 rounded-lg">
              <Title level={5} className="text-amber-800 mb-4">📖 历史背景</Title>
              <div className="prose prose-slate max-w-none">
                <OptimizedMarkdownContent content={item.knowledgeArchaeology.background || '暂无背景介绍'} />
              </div>
            </div>
            
            {item.knowledgeArchaeology.evolution && (
              <div className="bg-orange-50 p-6 rounded-lg">
                <Title level={5} className="text-orange-800 mb-4">🔄 演进历程</Title>
                <div className="prose prose-slate max-w-none">
                  <OptimizedMarkdownContent content={item.knowledgeArchaeology.evolution} />
                </div>
              </div>
            )}
          </div>
        )}

        {subTab === 'figures' && item.knowledgeArchaeology.keyFigures && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {item.knowledgeArchaeology.keyFigures.map((figure, index) => (
              <div key={index} className="bg-indigo-50 p-6 rounded-lg border border-indigo-200">
                <Title level={5} className="text-indigo-800 mb-2">
                  👤 {figure.name}
                </Title>
                <Tag color="indigo" className="mb-3">{figure.role}</Tag>
                <Paragraph className="text-indigo-700 mb-3">
                  {figure.contribution}
                </Paragraph>
                <div className="bg-white p-3 rounded border-l-4 border-indigo-400">
                  <Text className="text-indigo-600 text-sm">
                    🌟 意义：{figure.significance}
                  </Text>
                </div>
              </div>
            ))}
          </div>
        )}

        {subTab === 'concepts' && item.knowledgeArchaeology.concepts && (
          <div className="space-y-4">
            {item.knowledgeArchaeology.concepts.map((concept, index) => (
              <div key={index} className="bg-green-50 p-6 rounded-lg border border-green-200">
                <Title level={5} className="text-green-800 mb-3">
                  💡 {concept.term}
                </Title>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Text strong className="text-green-700">定义：</Text>
                    <Paragraph className="text-green-600 mt-1">
                      {concept.definition}
                    </Paragraph>
                  </div>
                  <div>
                    <Text strong className="text-green-700">现代意义：</Text>
                    <Paragraph className="text-green-600 mt-1">
                      {concept.modernRelevance}
                    </Paragraph>
                  </div>
                </div>
                <div className="mt-4 bg-white p-3 rounded border-l-4 border-green-400">
                  <Text strong className="text-green-700">演进过程：</Text>
                  <Paragraph className="text-green-600 mt-1">
                    {concept.evolution}
                  </Paragraph>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const renderCommonQuestions = (item: ApiItem, copyToClipboard: (text: string) => void, secondaryTabStates: Record<string, number>, setSecondaryTabStates: React.Dispatch<React.SetStateAction<Record<string, number>>>) => {
  const currentQuestionIndex = secondaryTabStates.commonQuestions || 0;
  
  if (!item.commonQuestions || item.commonQuestions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">❓</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无常见问题</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-xs">❓</span>
          </div>
          <Title level={4} className="!mb-0 text-slate-900 font-medium">常见问题</Title>
          <span className="text-slate-500 text-sm">开发中的常见问题与解决方案</span>
        </div>
        <div className="px-3 py-1.5 bg-slate-100 rounded-full">
          <span className="text-slate-700 text-sm font-medium">{item.commonQuestions.length} 个问题</span>
        </div>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {item.commonQuestions.map((qa, index) => (
          <button
            key={index}
            onClick={() => setSecondaryTabStates(prev => ({
              ...prev,
              commonQuestions: index
            }))}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              currentQuestionIndex === index
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <div className="flex items-center gap-2">
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                currentQuestionIndex === index
                  ? 'bg-slate-600 text-white'
                  : 'bg-slate-300 text-slate-600'
              }`}>
                {index + 1}
              </span>
              <span className="text-sm font-medium">问题 {index + 1}</span>
            </div>
            {currentQuestionIndex === index && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* 当前问题内容 */}
      {item.commonQuestions[currentQuestionIndex] && (
        <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
          <div className="space-y-6">
            {/* 问题 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl"></div>
              <div className="relative p-5 border border-blue-200/50 rounded-xl border-l-4 border-blue-400">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">❓</span>
                  </div>
                  <Text strong className="text-blue-700 font-semibold">问题</Text>
                </div>
                <Text className="text-slate-800 text-base leading-relaxed">
                  {item.commonQuestions[currentQuestionIndex].question}
                </Text>
              </div>
            </div>

            {/* 答案 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl"></div>
              <div className="relative p-5 border border-emerald-200/50 rounded-xl border-l-4 border-emerald-400">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">💡</span>
                  </div>
                  <Text strong className="text-emerald-700 font-semibold">解答</Text>
                </div>
                <div className="prose prose-slate max-w-none">
                  <OptimizedMarkdownContent content={item.commonQuestions[currentQuestionIndex].answer} />
                </div>
              </div>
            </div>

            {/* 代码示例 */}
            {item.commonQuestions[currentQuestionIndex].code && (
              <div className="relative border border-slate-200/50 rounded-xl bg-white/90 backdrop-blur-sm p-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">🔧</span>
                  </div>
                  <Text strong className="text-slate-800 font-semibold">相关代码</Text>
                </div>
                <CodeHighlight
                  code={item.commonQuestions[currentQuestionIndex].code}
                  language="javascript"
                  title="示例代码"
                  onCopy={() => copyToClipboard(item.commonQuestions[currentQuestionIndex].code!)}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 调试技巧组件 - 修复Hook规则违反问题
const DebuggingTipsComponent: React.FC<{ item: ApiItem; copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  // 🆕 处理新的subTabs结构
  const [activeSubTab, setActiveSubTab] = useState(
    item.debuggingTips?.subTabs?.[0]?.key || ''
  );

  // 如果是字符串内容（markdown），直接渲染
  if (typeof item.debuggingTips === 'string') {
    return (
      <div className="prose max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={{
            pre: ({ children }) => <>{children}</>,
          }}
        >
          {item.debuggingTips}
        </ReactMarkdown>
      </div>
    );
  }

  if (!item.debuggingTips) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🐛</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无调试技巧内容</Text>
        </div>
      </div>
    );
  }

  // 🆕 处理新的subTabs结构
  if (item.debuggingTips.subTabs && item.debuggingTips.subTabs.length > 0) {

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-xs">🐛</span>
          </div>
          <Title level={4} className="!mb-0 text-slate-900 font-medium">调试技巧</Title>
          <span className="text-slate-500 text-sm">调试方法与问题诊断</span>
        </div>

        {/* 子Tab导航 */}
        <div className="flex gap-2 p-1 bg-slate-100 rounded-lg">
          {item.debuggingTips.subTabs.map((subTab) => (
            <button
              key={subTab.key}
              onClick={() => setActiveSubTab(subTab.key)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                activeSubTab === subTab.key
                  ? 'bg-white text-slate-900 shadow-sm'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
              }`}
            >
              {subTab.title}
            </button>
          ))}
        </div>

        {/* 子Tab内容 */}
        {item.debuggingTips.subTabs.map((subTab) => (
          activeSubTab === subTab.key && (
            <div key={subTab.key} className="space-y-6">
              {/* 介绍 */}
              {subTab.content.introduction && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200/50">
                  <Text className="text-blue-800 text-sm leading-relaxed">
                    {subTab.content.introduction}
                  </Text>
                </div>
              )}

              {/* 内容区块 */}
              {subTab.content.sections.map((section, sectionIndex) => (
                <div key={sectionIndex} className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-slate-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-xs">📋</span>
                    </div>
                    <Title level={5} className="mb-0 text-slate-900 font-medium">
                      {section.title}
                    </Title>
                  </div>

                  {section.description && (
                    <Text className="text-slate-600 text-sm leading-relaxed">
                      {section.description}
                    </Text>
                  )}

                  <div className="space-y-4">
                    {section.items.map((item, itemIndex) => (
                      <AntCard key={itemIndex} className="border border-slate-200 hover:border-slate-300 transition-colors">
                        <div className="space-y-3">
                          {/* 标题 */}
                          <div className="bg-slate-50 p-3 rounded-lg">
                            <Text strong className="text-slate-900">{item.title}</Text>
                          </div>

                          {/* 描述 */}
                          <div className="bg-blue-50 p-3 rounded-lg">
                            <Text className="text-blue-800 text-sm leading-relaxed">
                              {item.description}
                            </Text>
                          </div>

                          {/* 解决方案 */}
                          {item.solution && (
                            <div className="bg-green-50 p-3 rounded-lg">
                              <Text strong className="text-green-800">解决方案：</Text>
                              <Text className="text-green-700 text-sm mt-1">{item.solution}</Text>
                            </div>
                          )}

                          {/* 预防措施 */}
                          {item.prevention && (
                            <div className="bg-yellow-50 p-3 rounded-lg">
                              <Text strong className="text-yellow-800">预防措施：</Text>
                              <Text className="text-yellow-700 text-sm mt-1">{item.prevention}</Text>
                            </div>
                          )}

                          {/* 步骤 */}
                          {item.steps && item.steps.length > 0 && (
                            <div className="bg-indigo-50 p-3 rounded-lg">
                              <Text strong className="text-indigo-800">操作步骤：</Text>
                              <ol className="mt-2 space-y-1">
                                {item.steps.map((step, stepIndex) => (
                                  <li key={stepIndex} className="text-indigo-700 text-sm">
                                    {stepIndex + 1}. {step}
                                  </li>
                                ))}
                              </ol>
                            </div>
                          )}

                          {/* 技巧 */}
                          {item.tips && item.tips.length > 0 && (
                            <div className="bg-purple-50 p-3 rounded-lg">
                              <Text strong className="text-purple-800">实用技巧：</Text>
                              <ul className="mt-2 space-y-1">
                                {item.tips.map((tip, tipIndex) => (
                                  <li key={tipIndex} className="text-purple-700 text-sm">
                                    • {tip}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {/* 代码示例 */}
                          {item.code && (
                            <div className="space-y-2">
                              <Text strong className="text-gray-800 block">代码示例：</Text>
                              <CodeHighlight
                                code={item.code}
                                language="javascript"
                                title="示例代码"
                                onCopy={() => copyToClipboard(item.code!)}
                              />
                            </div>
                          )}
                        </div>
                      </AntCard>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )
        ))}
      </div>
    );
  }

  // 🔄 兼容旧版本的平铺结构 - 渲染结构化的DebuggingTips对象
  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🔧</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">调试技巧</Title>
        <span className="text-slate-500 text-sm">调试方法与问题诊断</span>
      </div>
      
      {/* 常见错误 */}
      {item.debuggingTips.commonErrors && item.debuggingTips.commonErrors.length > 0 && (
        <div>
          <Title level={5}>❌ 常见错误</Title>
          <div className="space-y-4">
            {item.debuggingTips.commonErrors.map((error, index) => (
              <AntCard key={index} className="error-card">
                <div className="space-y-3">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <Text strong style={{ color: '#dc2626' }}>错误：</Text>
                    <Text>{error.error}</Text>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <Text strong style={{ color: '#ea580c' }}>原因：</Text>
                    <Text>{error.cause}</Text>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <Text strong style={{ color: '#059669' }}>解决方案：</Text>
                    <Text>{error.solution}</Text>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <Text strong style={{ color: '#0369a1' }}>预防措施：</Text>
                    <Text>{error.prevention}</Text>
                  </div>
                  {error.code && (
                    <CodeHighlight 
                      code={error.code} 
                      language="javascript" 
                      title="示例代码"
                      onCopy={() => copyToClipboard(error.code || '')}
                    />
                  )}
                </div>
              </AntCard>
            ))}
          </div>
        </div>
      )}
      
      {/* 开发工具技巧 */}
      {item.debuggingTips.devToolsTips && item.debuggingTips.devToolsTips.length > 0 && (
        <div>
          <Title level={5}>🛠️ 开发工具技巧</Title>
          <div className="space-y-4">
            {item.debuggingTips.devToolsTips.map((tip, index) => (
              <AntCard key={index} className="tool-tip-card">
                <div className="space-y-3">
                  <div>
                    <Text strong style={{ color: '#6366f1' }}>工具：</Text>
                    <Text>{tip.tool}</Text>
                  </div>
                  <div>
                    <Text strong style={{ color: '#059669' }}>技巧：</Text>
                    <Text>{tip.technique}</Text>
                  </div>
                  <div>
                    <Text strong style={{ color: '#ea580c' }}>示例：</Text>
                    <Text>{tip.example}</Text>
                  </div>
                </div>
              </AntCard>
            ))}
          </div>
        </div>
      )}
      
      {/* 问题排查 */}
      {item.debuggingTips.troubleshooting && item.debuggingTips.troubleshooting.length > 0 && (
        <div>
          <Title level={5}>🔍 问题排查</Title>
          <div className="space-y-4">
            {item.debuggingTips.troubleshooting.map((issue, index) => (
              <AntCard key={index} className="troubleshooting-card">
                <div className="space-y-3">
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <Text strong style={{ color: '#ca8a04' }}>症状：</Text>
                    <Text>{issue.symptom}</Text>
                  </div>
                  <div>
                    <Text strong style={{ color: '#dc2626' }}>可能原因：</Text>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {issue.possibleCauses.map((cause, causeIndex) => (
                        <li key={causeIndex}><Text>{cause}</Text></li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <Text strong style={{ color: '#059669' }}>解决方案：</Text>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {issue.solutions.map((solution, solutionIndex) => (
                        <li key={solutionIndex}><Text>{solution}</Text></li>
                      ))}
                    </ul>
                  </div>
                </div>
              </AntCard>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 性能优化组件 - 现代化专业设计，使用子Tab结构
const PerformanceOptimizationComponent: React.FC<{ item: ApiItem }> = ({ item }) => {
  const [subTab, setSubTab] = useState('strategies');

  if (!item.performanceOptimization) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">⚡</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无性能优化内容</Text>
        </div>
      </div>
    );
  }

  const subTabs = [
    { key: 'strategies', label: '📈 优化策略', condition: item.performanceOptimization.optimizationStrategies },
    { key: 'practices', label: '✨ 最佳实践', condition: item.performanceOptimization.bestPractices },
    { key: 'benchmarks', label: '📊 性能基准', condition: item.performanceOptimization.benchmarks },
    { key: 'monitoring', label: '🔍 监控方案', condition: item.performanceOptimization.monitoring }
  ].filter(tab => tab.condition);

  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">⚡</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">性能优化</Title>
        <span className="text-slate-500 text-sm">性能分析与优化策略</span>
      </div>

      {/* 现代化Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50">
        {subTabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setSubTab(tab.key)}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              subTab === tab.key
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="text-sm font-medium">{tab.label}</span>
            {subTab === tab.key && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* Tab内容区域 */}
      <div className="min-h-[400px]">
        {subTab === 'strategies' && item.performanceOptimization.optimizationStrategies && (
          <div className="space-y-6">
            {item.performanceOptimization.optimizationStrategies.map((strategy, index) => (
              <div key={index} className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
                <div className="space-y-4">
                  {/* 策略标题 */}
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-gray-700 rounded-xl flex items-center justify-center">
                      <span className="text-white font-bold">{index + 1}</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-1 text-slate-900">{strategy.strategy}</Title>
                      <Text className="text-slate-600">{strategy.description}</Text>
                    </div>
                  </div>

                  {/* 实现方案 - 使用现代化代码块 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-800 font-semibold">🔧 实现方案</span>
                    </div>
                    <CodeHighlight
                      code={strategy.implementation}
                      language="javascript"
                      title={`${strategy.strategy} - 代码实现`}
                      showLineNumbers={true}
                    />
                  </div>

                  {/* 性能影响 */}
                  <div className="bg-green-50 p-4 rounded-xl border border-green-200/50">
                    <Text strong className="text-green-800 block mb-2">📈 性能影响</Text>
                    <Text className="text-green-700">{strategy.impact}</Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {subTab === 'practices' && item.performanceOptimization.bestPractices && (
          <div className="space-y-4">
            {item.performanceOptimization.bestPractices.map((practice, index) => (
              <div key={index} className="border border-cyan-200 rounded-lg p-4 bg-cyan-50">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">{index + 1}</span>
                    </div>
                    <Text strong className="text-cyan-900 text-base">{practice.practice}</Text>
                  </div>
                  <Text className="text-cyan-800 block leading-relaxed">{practice.description}</Text>
                  {practice.example && (
                    <div className="mt-3">
                      <CodeHighlight 
                        code={practice.example} 
                        language="javascript"
                        title={practice.practice}
                        onCopy={() => navigator.clipboard.writeText(practice.example)}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {subTab === 'benchmarks' && item.performanceOptimization.benchmarks && (
          <div className="space-y-6">
            {item.performanceOptimization.benchmarks.map((benchmark, index) => (
              <div key={index} className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
                <div className="space-y-4">
                  {/* 基准标题 */}
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-xl flex items-center justify-center">
                      <span className="text-white font-bold">{index + 1}</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-1 text-slate-900">{benchmark.scenario}</Title>
                      <Text className="text-slate-600">{benchmark.description}</Text>
                    </div>
                  </div>

                  {/* 性能指标 */}
                  <div className="bg-purple-50 p-4 rounded-xl border border-purple-200/50">
                    <Text strong className="text-purple-800 block mb-3">📊 性能指标</Text>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {Object.entries(benchmark.metrics).map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center bg-white p-2 rounded-lg">
                          <Text className="text-purple-700 font-medium">{key}:</Text>
                          <Text className="text-purple-600">{value}</Text>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 结论 */}
                  <div className="bg-amber-50 p-4 rounded-xl border border-amber-200/50">
                    <Text strong className="text-amber-800 block mb-2">💡 结论</Text>
                    <Text className="text-amber-700">{benchmark.conclusion}</Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {subTab === 'monitoring' && item.performanceOptimization.monitoring && (
          <div className="space-y-6">
            {/* 监控工具 */}
            {item.performanceOptimization.monitoring.tools && (
              <div>
                <Title level={5} className="text-slate-900 mb-4">🛠️ 监控工具</Title>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {item.performanceOptimization.monitoring.tools.map((tool, index) => (
                    <div key={index} className="bg-slate-50 p-4 rounded-xl border border-slate-200/50">
                      <Title level={5} className="text-slate-800 mb-2">{tool.name}</Title>
                      <Text className="text-slate-600 block mb-2">{tool.description}</Text>
                      <div className="bg-white p-2 rounded-lg">
                        <Text className="text-slate-700 text-sm">{tool.usage}</Text>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 监控指标 */}
            {item.performanceOptimization.monitoring.metrics && (
              <div>
                <Title level={5} className="text-slate-900 mb-4">📈 监控指标</Title>
                <div className="space-y-3">
                  {item.performanceOptimization.monitoring.metrics.map((metric, index) => (
                    <div key={index} className="bg-emerald-50 p-4 rounded-xl border border-emerald-200/50">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Text strong className="text-emerald-800 block">指标名称</Text>
                          <Text className="text-emerald-700">{metric.metric}</Text>
                        </div>
                        <div>
                          <Text strong className="text-emerald-800 block">目标值</Text>
                          <Text className="text-emerald-700">{metric.target}</Text>
                        </div>
                        <div>
                          <Text strong className="text-emerald-800 block">测量方式</Text>
                          <Text className="text-emerald-700">{metric.measurement}</Text>
                        </div>
                      </div>
                      <div className="mt-2">
                        <Text className="text-emerald-600 text-sm">{metric.description}</Text>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// 本质洞察组件 - 深度哲学思辨设计
const EssenceInsightsComponent: React.FC<{ item: ApiItem }> = ({ item }) => {
  const [subTab, setSubTab] = useState('problem');

  if (!item.essenceInsights) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-lg border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🔮</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无本质洞察内容</Text>
        </div>
      </div>
    );
  }

  const subTabs = [
    { key: 'problem', label: '🎯 核心问题' },
    { key: 'design', label: '🧠 设计智慧' },
    { key: 'insight', label: '💡 应用洞察' },
    { key: 'architecture', label: '🏗️ 架构思考' }
  ];

  return (
    <div className="space-y-6">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🔮</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">本质洞察</Title>
        <span className="text-slate-500 text-sm">认知跃迁的催化剂</span>
      </div>

      {/* Tab导航 */}
      <div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50 mb-6">
        {subTabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setSubTab(tab.key)}
            className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
              subTab === tab.key
                ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="text-sm font-medium">{tab.label}</span>
            {subTab === tab.key && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* 深度思辨内容区域 */}
      <div className="min-h-[300px] space-y-4">
        {subTab === 'problem' && (
          <div className="prose prose-slate max-w-none">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">🎯 核心问题</h3>
            {/* 支持新的四重境界结构 */}
            {item.essenceInsights.subTabs?.problem ? (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-blue-900 mb-3">引言</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.problem.introduction} />

                  {/* 核心洞察汇总图 */}
                  {item.essenceInsights.subTabs.problem.coreInsightDiagram && (
                    <div className="mt-6 bg-white/70 p-4 rounded-lg border border-blue-200">
                      <h5 className="text-lg font-semibold text-blue-800 mb-3">🎯 核心洞察汇总图</h5>
                      <div className="bg-white p-4 rounded-md border border-blue-300/50">
                        <MermaidChart
                          chart={item.essenceInsights.subTabs.problem.coreInsightDiagram}
                          title="Promise核心本质洞察图"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 border-l-4 border-purple-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-purple-900 mb-3">{item.essenceInsights.subTabs.problem.complexityAnalysis.title}</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.problem.complexityAnalysis.description} />
                  <div className="mt-4 space-y-3">
                    {item.essenceInsights.subTabs.problem.complexityAnalysis.layers.map((layer, index) => (
                      <div key={index} className="bg-white/70 p-4 rounded-lg border border-purple-200">
                        <h5 className="font-semibold text-purple-800 mb-2">{layer.level} - {layer.question}</h5>
                        <OptimizedMarkdownContent content={layer.analysis} />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-amber-900 mb-3">{item.essenceInsights.subTabs.problem.fundamentalDilemma.title}</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.problem.fundamentalDilemma.description} />
                  <div className="mt-4 bg-white/70 p-4 rounded-lg border border-amber-200">
                    <h5 className="font-semibold text-amber-800 mb-2">根本原因</h5>
                    <OptimizedMarkdownContent content={item.essenceInsights.subTabs.problem.fundamentalDilemma.rootCause} />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-green-900 mb-3">{item.essenceInsights.subTabs.problem.layeredInquiry.title}</h4>
                  <div className="space-y-3">
                    {item.essenceInsights.subTabs.problem.layeredInquiry.questionChain.map((inquiry, index) => (
                      <div key={index} className="bg-white/70 p-4 rounded-lg border border-green-200">
                        <h5 className="font-semibold text-green-800 mb-2">{inquiry.layer}层：{inquiry.question}</h5>
                        <OptimizedMarkdownContent content={inquiry.answer} />
                        {inquiry.nextQuestion && (
                          <div className="mt-2 text-sm text-green-600 italic">→ {inquiry.nextQuestion}</div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              /* 兼容旧结构 */
              <div className="bg-slate-50 border-l-4 border-slate-400 p-4 mb-6">
                <OptimizedMarkdownContent content={item.essenceInsights.coreQuestion || '暂无内容'} />
              </div>
            )}
          </div>
        )}

        {subTab === 'design' && (
          <div className="prose prose-slate max-w-none">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">🧠 设计智慧</h3>
            {/* 支持新的四重境界结构 */}
            {item.essenceInsights.subTabs?.design ? (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-blue-900 mb-3">引言</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.design.introduction} />
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 border-l-4 border-purple-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-purple-900 mb-3">{item.essenceInsights.subTabs.design.minimalism.title}</h4>
                  <div className="space-y-3">
                    <div className="bg-white/70 p-4 rounded-lg border border-purple-200">
                      <h5 className="font-semibold text-purple-800 mb-2">接口设计</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.design.minimalism.interfaceDesign} />
                    </div>
                    <div className="bg-white/70 p-4 rounded-lg border border-purple-200">
                      <h5 className="font-semibold text-purple-800 mb-2">设计哲学</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.design.minimalism.philosophy} />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-amber-900 mb-3">{item.essenceInsights.subTabs.design.tradeoffWisdom.title}</h4>
                  <div className="space-y-3">
                    {item.essenceInsights.subTabs.design.tradeoffWisdom.tradeoffs.map((tradeoff, index) => (
                      <div key={index} className="bg-white/70 p-4 rounded-lg border border-amber-200">
                        <h5 className="font-semibold text-amber-800 mb-2">{tradeoff.dimension1} vs {tradeoff.dimension2}</h5>
                        <OptimizedMarkdownContent content={tradeoff.analysis} />
                        <div className="mt-2 text-sm text-amber-700 italic">
                          <OptimizedMarkdownContent content={tradeoff.reasoning} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              /* 兼容旧结构 */
              <div className="space-y-4">
                <div className="bg-slate-50 border-l-4 border-slate-400 p-4">
                  <OptimizedMarkdownContent content={item.essenceInsights.designPhilosophy?.worldview || '暂无内容'} />
                </div>
                <div className="bg-slate-50 border-l-4 border-slate-400 p-4">
                  <OptimizedMarkdownContent content={item.essenceInsights.designPhilosophy?.methodology || '暂无内容'} />
                </div>
              </div>
            )}
          </div>
        )}

        {subTab === 'insight' && (
          <div className="prose prose-slate max-w-none">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">💡 应用洞察</h3>
            {/* 支持新的四重境界结构 */}
            {item.essenceInsights.subTabs?.insight ? (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-blue-900 mb-3">引言</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.insight.introduction} />
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-green-900 mb-3">{item.essenceInsights.subTabs.insight.stateSync.title}</h4>
                  <div className="space-y-3">
                    <div className="bg-white/70 p-4 rounded-lg border border-green-200">
                      <h5 className="font-semibold text-green-800 mb-2">本质</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.insight.stateSync.essence} />
                    </div>
                    <div className="bg-white/70 p-4 rounded-lg border border-green-200">
                      <h5 className="font-semibold text-green-800 mb-2">真正价值</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.insight.stateSync.realValue} />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 border-l-4 border-purple-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-purple-900 mb-3">{item.essenceInsights.subTabs.insight.realWorldInsights.title}</h4>
                  <div className="space-y-3">
                    {item.essenceInsights.subTabs.insight.realWorldInsights.scenarios.map((scenario, index) => (
                      <div key={index} className="bg-white/70 p-4 rounded-lg border border-purple-200">
                        <h5 className="font-semibold text-purple-800 mb-2">{scenario.scenario}</h5>
                        <OptimizedMarkdownContent content={scenario.insight} />
                        <div className="mt-2 text-sm text-purple-700">
                          <OptimizedMarkdownContent content={scenario.deeperValue} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              /* 兼容旧结构 */
              <div className="space-y-4">
                <div className="bg-slate-50 border-l-4 border-slate-400 p-4">
                  <OptimizedMarkdownContent content={item.essenceInsights.hiddenTruth?.surfaceProblem || '暂无内容'} />
                </div>
                <div className="bg-slate-50 border-l-4 border-slate-400 p-4">
                  <OptimizedMarkdownContent content={item.essenceInsights.hiddenTruth?.realProblem || '暂无内容'} />
                </div>
              </div>
            )}
          </div>
        )}

        {subTab === 'architecture' && (
          <div className="prose prose-slate max-w-none">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">🏗️ 架构思考</h3>
            {/* 支持新的四重境界结构 */}
            {item.essenceInsights.subTabs?.architecture ? (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-blue-900 mb-3">引言</h4>
                  <OptimizedMarkdownContent content={item.essenceInsights.subTabs.architecture.introduction} />
                </div>

                <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-amber-900 mb-3">{item.essenceInsights.subTabs.architecture.ecosystemEvolution.title}</h4>
                  <div className="space-y-3">
                    <div className="bg-white/70 p-4 rounded-lg border border-amber-200">
                      <h5 className="font-semibold text-amber-800 mb-2">历史意义</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.architecture.ecosystemEvolution.historicalSignificance} />
                    </div>
                    <div className="bg-white/70 p-4 rounded-lg border border-amber-200">
                      <h5 className="font-semibold text-amber-800 mb-2">未来影响</h5>
                      <OptimizedMarkdownContent content={item.essenceInsights.subTabs.architecture.ecosystemEvolution.futureImpact} />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                  <h4 className="text-lg font-semibold text-green-900 mb-3">{item.essenceInsights.subTabs.architecture.architecturalInsights.title}</h4>
                  <div className="bg-white/70 p-4 rounded-lg border border-green-200 mb-4">
                    <h5 className="font-semibold text-green-800 mb-2">普世智慧</h5>
                    <OptimizedMarkdownContent content={item.essenceInsights.subTabs.architecture.architecturalInsights.universalWisdom} />
                  </div>
                  <div className="space-y-3">
                    {item.essenceInsights.subTabs.architecture.architecturalInsights.principles.map((principle, index) => (
                      <div key={index} className="bg-white/70 p-4 rounded-lg border border-green-200">
                        <h5 className="font-semibold text-green-800 mb-2">{principle.principle}</h5>
                        <OptimizedMarkdownContent content={principle.explanation} />
                        <div className="mt-2 text-sm text-green-700 italic">
                          <OptimizedMarkdownContent content={principle.universality} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              /* 兼容旧结构 */
              <div className="space-y-4">
                {item.essenceInsights.deeperQuestions?.map((question, index) => (
                  <div key={index} className="bg-slate-50 border-l-4 border-slate-400 p-4">
                    <h4 className="text-sm font-medium text-slate-800 mb-2">层次 {question.layer}: {question.question}</h4>
                    <OptimizedMarkdownContent content={question.why} />
                  </div>
                )) || <div className="text-slate-500">暂无内容</div>}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const renderImplementation = (item: ApiItem) => <ImplementationComponent item={item} />;

const renderKnowledgeArchaeology = (item: ApiItem) => <KnowledgeArchaeologyComponent item={item} />;

const renderEssenceInsights = (item: ApiItem) => <EssenceInsightsComponent item={item} />;

// 性能优化渲染函数
const renderPerformanceOptimization = (item: ApiItem, secondaryTabStates: Record<string, number>, setSecondaryTabStates: React.Dispatch<React.SetStateAction<Record<string, number>>>) => {
  // 如果是字符串内容（markdown），直接渲染
  if (typeof item.performanceOptimization === 'string') {
    return (
      <div className="prose max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={{
            pre: ({ children }) => <>{children}</>,
          }}
        >
          {item.performanceOptimization}
        </ReactMarkdown>
      </div>
    );
  }

  return <PerformanceOptimizationComponent item={item} />;
};

// 学习路径渲染函数
const renderLearningPath = (item: ApiItem) => (
  <div className="space-y-6">
    <Title level={4}>学习路径</Title>
    
    {item.learningPath?.prerequisites && (
      <div>
        <Title level={5}>前置知识</Title>
        <div className="space-y-2">
          {(() => {
            const prerequisites = item.learningPath.prerequisites;
            const prerequisitesList = Array.isArray(prerequisites) ? prerequisites : [];
            
            return prerequisitesList.map((prereq, index) => (
              <div key={index} className="flex items-center gap-2 p-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <Text>{prereq}</Text>
              </div>
            ));
          })()}
        </div>
      </div>
    )}
    
    {item.learningPath?.learningSteps && (
      <div>
        <Title level={5}>学习步骤</Title>
        {item.learningPath.learningSteps.map((step) => (
          <AntCard
            key={step.step}
            title={
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
                  {step.step}
                </div>
                <span>{step.title}</span>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            <Paragraph>{step.description}</Paragraph>
            
            {step.resources && (
              <div className="mt-4">
                <Text strong>学习资源:</Text>
                <ul className="mt-2">
                  {step.resources.map((resource, index) => (
                    <li key={index}>{resource}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {step.practiceExercises && (
              <div className="mt-4">
                <Text strong>练习项目:</Text>
                <div className="mt-2 grid gap-2">
                  {step.practiceExercises.map((exercise, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded">
                      <div className="flex items-center justify-between mb-1">
                        <Text strong>{exercise.title}</Text>
                        <div className="flex gap-2">
                          <AntBadge 
                            color={exercise.difficulty === 'hard' ? 'red' : exercise.difficulty === 'medium' ? 'orange' : 'green'}
                            text={exercise.difficulty}
                          />
                          <Text type="secondary">{exercise.estimatedTime}</Text>
                        </div>
                      </div>
                      <Text type="secondary">{exercise.description}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </AntCard>
        ))}
      </div>
    )}
    
    {item.learningPath && (
      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <Text strong>预计学习时间: </Text>
            <Text>{item.learningPath.estimatedTime}</Text>
          </div>
          <div>
            <Text strong>技能等级: </Text>
            <AntBadge 
              color={item.learningPath.skillLevel === 'advanced' ? 'red' : item.learningPath.skillLevel === 'intermediate' ? 'orange' : 'green'}
              text={item.learningPath.skillLevel}
            />
          </div>
        </div>
      </div>
    )}
    
    {item.learningPath?.nextSteps && (
      <div>
        <Title level={5}>下一步学习</Title>
        <div className="space-y-2">
          {(() => {
            const nextSteps = item.learningPath.nextSteps;
            const nextStepsList = Array.isArray(nextSteps) ? nextSteps : [];
            
            return nextStepsList.map((step, index) => (
              <div key={index} className="flex items-center gap-2 p-2">
                <span className="text-blue-500">→</span>
                <Text>{step}</Text>
              </div>
            ));
          })()}
        </div>
      </div>
    )}
  </div>
);

// 版本迁移渲染函数
const renderVersionMigration = (item: ApiItem) => {
  if (!item.versionMigration) {
    return <Paragraph>暂无版本迁移内容</Paragraph>;
  }

  return (
    <div className="space-y-6">
      <Title level={4}>版本迁移指南</Title>
      <Paragraph>版本迁移功能正在完善中...</Paragraph>
    </div>
  );
};

// 生态工具渲染函数
const renderEcosystemTools = (item: ApiItem) => {
  if (!item.ecosystemTools) {
    return <Paragraph>暂无生态工具内容</Paragraph>;
  }

  return (
    <div className="space-y-6">
      <Title level={4}>生态工具</Title>
      <Paragraph>生态工具功能正在完善中...</Paragraph>
    </div>
  );
};

// 实战项目渲染函数
const renderRealWorldProjects = (item: ApiItem, copyToClipboard: (text: string) => void) => {
  // 如果是字符串内容（markdown），直接渲染
  if (typeof item.realWorldProjects === 'string') {
    return (
      <div className="prose max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={{
            pre: ({ children }) => <>{children}</>,
          }}
        >
          {item.realWorldProjects}
        </ReactMarkdown>
      </div>
    );
  }

  if (!item.realWorldProjects) {
    return <Paragraph>暂无实战项目内容</Paragraph>;
  }

  // 安全访问projects属性
  const realWorldData = item.realWorldProjects as RealWorldData;
  const projects = realWorldData.projects || [];
  
  if (projects.length === 0) {
    return <Paragraph>暂无实战项目内容</Paragraph>;
  }

  return (
    <div className="space-y-6">
      <Title level={4}>实战项目案例</Title>
      
      {projects.map((project: ProjectData, index: number) => (
        <AntCard
          key={index}
          title={
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ProjectOutlined style={{ fontSize: '20px', color: '#8b5cf6' }} />
                <span className="text-lg font-semibold">{project.title}</span>
              </div>
              <div className="flex gap-2">
                <AntBadge 
                  color={
                    project.complexity === 'complex' || project.difficulty === 'hard' ? 'red' : 
                    project.complexity === 'medium' || project.difficulty === 'medium' ? 'orange' : 
                    'green'
                  }
                  text={
                    project.complexity === 'complex' || project.difficulty === 'hard' ? '高级' : 
                    project.complexity === 'medium' || project.difficulty === 'medium' ? '中级' : 
                    '简单'
                  }
                />
              </div>
            </div>
          }
          style={{ marginBottom: '24px' }}
          className="project-card"
        >
          <div className="space-y-4">
            {/* 项目描述 */}
            <div>
              <Text strong style={{ color: '#6366f1', marginBottom: '8px', display: 'block' }}>
                📝 项目描述
              </Text>
              <Paragraph style={{ lineHeight: '1.6' }}>
                {project.description}
              </Paragraph>
            </div>

            {/* 技术亮点 */}
            {project.highlights && project.highlights.length > 0 && (
              <div>
                <Text strong style={{ color: '#059669', marginBottom: '8px', display: 'block' }}>
                  ✨ 技术亮点
                </Text>
                <ul className="space-y-2">
                  {project.highlights.map((highlight: string, idx: number) => (
                    <li key={idx} className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">✓</span>
                      <span>{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* 使用场景 */}
            {project.useCase && (
              <div>
                <Text strong style={{ color: '#059669', marginBottom: '8px', display: 'block' }}>
                  🎯 使用场景
                </Text>
                <Paragraph>{project.useCase}</Paragraph>
              </div>
            )}

            {/* 核心代码 */}
            <div>
              <Text strong style={{ color: '#dc2626', marginBottom: '12px', display: 'block' }}>
                💻 核心实现
              </Text>
              <CodeHighlight 
                code={project.codeExample} 
                language="javascript"
                title={`${project.title} - 核心代码`}
                onCopy={() => copyToClipboard(project.codeExample)}
              />
            </div>

            {/* 技术栈标签 */}
            {project.technologies && project.technologies.length > 0 && (
              <div className="flex flex-wrap gap-2 pt-3 border-t border-gray-200">
                {project.technologies.map((tech: string, techIndex: number) => (
                  <AntBadge 
                    key={techIndex}
                    color="blue" 
                    text={tech} 
                    style={{ 
                      backgroundColor: '#e0f2fe', 
                      color: '#0369a1',
                      border: '1px solid #bae6fd'
                    }}
                  />
                ))}
              </div>
            )}

            {/* GitHub链接 */}
            {project.githubLink && (
              <div className="pt-3 border-t border-gray-200">
                <a 
                  href={project.githubLink} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>在GitHub上查看完整代码</span>
                </a>
              </div>
            )}
          </div>
        </AntCard>
      ))}

      {/* 设计模式部分 */}
      {realWorldData.patterns && realWorldData.patterns.length > 0 && (
        <div className="mt-8">
          <Title level={5}>🎨 相关设计模式</Title>
          <div className="space-y-4">
            {realWorldData.patterns.map((pattern: PatternData, index: number) => (
              <AntCard
                key={index}
                title={pattern.name}
                style={{ marginBottom: '16px' }}
              >
                <div className="space-y-3">
                  <div>
                    <Text strong>描述: </Text>
                    <Text>{pattern.description}</Text>
                  </div>
                  <div>
                    <Text strong>何时使用: </Text>
                    <Text>{pattern.when}</Text>
                  </div>
                  <div>
                    <CodeHighlight 
                      code={pattern.code} 
                      language="javascript"
                      title={`${pattern.name} 模式`}
                      onCopy={() => copyToClipboard(pattern.code)}
                    />
                  </div>
                </div>
              </AntCard>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 记忆化的卡片组件
const ApiCard = memo<{
  item: ApiItem;
  currentTheme: { borderColor: string };
  favorites: string[];
  onCardClick: (item: ApiItem) => void;
  onToggleFavorite: (id: string) => void;
  onCopyToClipboard: (text: string) => void;
}>(({ item, currentTheme, favorites, onCardClick, onToggleFavorite, onCopyToClipboard }) => {
  // 记忆化计算卡片内容
  const cardContent = useMemo(() => {
    // 安全处理example字段，防止undefined导致的split错误
    const exampleText = item.example || '';
    const exampleLines = exampleText.split('\n');
    
    return {
      truncatedExample: exampleLines.slice(0, 8).join('\n'),
      remainingLines: Math.max(0, exampleLines.length - 8),
      difficultyText: item.difficulty === 'easy' ? '🟢 简单' : 
                     item.difficulty === 'medium' ? '🟡 中级' : '🔴 高级',
      difficultyClass: item.difficulty === 'easy' ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200/50' :
                      item.difficulty === 'medium' ? 'bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-700 border-yellow-200/50' :
                      'bg-gradient-to-r from-red-50 to-pink-50 text-red-700 border-red-200/50'
    };
  }, [item.example, item.difficulty]);

  // 优化点击处理函数
  const handleClick = useCallback(() => {
    onCardClick(item);
  }, [onCardClick, item]);

  const handleFavoriteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(item.id);
  }, [onToggleFavorite, item.id]);

  const handleCopyClick = useCallback((e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    onCopyToClipboard(text);
  }, [onCopyToClipboard]);

  return (
    <Card 
      className={`break-inside-avoid mb-4 group relative overflow-hidden transition-all duration-500 cursor-pointer border-0 shadow-lg hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-2 hover:scale-[1.02] bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl rounded-2xl min-h-[480px] ${currentTheme.borderColor.replace('border-l-', 'border-l-4 border-l-')} before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/50 before:to-transparent before:pointer-events-none`}
      onClick={handleClick}
    >
      {/* 卡片背景光效 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      
      <CardHeader className="pb-6 relative z-10">
        <div className="flex justify-between items-start gap-3 mb-4">
          <CardTitle className="text-xl font-bold text-neutral-900 dark:text-neutral-100 leading-tight flex items-center gap-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            <span className="relative">
              {item.title}
              {item.isNew && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-orange-400 to-red-400 rounded-full animate-pulse shadow-lg"></span>
              )}
            </span>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Star 
              className={`w-5 h-5 cursor-pointer transition-all duration-300 ${
                favorites.includes(item.id) 
                  ? 'text-yellow-500 fill-yellow-500 scale-110' 
                  : 'text-neutral-400 hover:text-yellow-500 hover:scale-110'
              }`}
              onClick={handleFavoriteClick}
            />
          </div>
        </div>
        
        {/* 现代化标签组 */}
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <Badge variant="secondary" className="text-xs font-medium bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border-blue-200/50 px-3 py-1 rounded-full">
            🏷️ {item.category}
          </Badge>
          {item.version && (
            <Badge variant="outline" className="text-xs font-medium bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200/50 px-3 py-1 rounded-full">
              📦 {item.version}
            </Badge>
          )}
          {item.difficulty && (
            <Badge variant="outline" className={`text-xs font-medium px-3 py-1 rounded-full ${cardContent.difficultyClass}`}>
              {cardContent.difficultyText}
            </Badge>
          )}
        </div>
        
        <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed font-medium">
          {item.description}
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6 pt-0 relative z-10">
        {/* 现代化语法展示 */}
        <div className="group/syntax">
          <div className="flex justify-between items-center mb-3">
            <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2">
              ⚡ 语法
            </h4>
            <Copy 
              className="w-4 h-4 text-neutral-400 hover:text-blue-500 cursor-pointer transition-all duration-300 opacity-0 group-hover/syntax:opacity-100 hover:scale-110"
              onClick={(e) => handleCopyClick(e, item.syntax)}
            />
          </div>
          <CodeHighlight 
            code={item.syntax} 
            language="javascript"
            isPreview={true}
            showLineNumbers={false}
          />
        </div>

        {/* 高级示例预览 */}
        <div className="group/example">
          <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mb-3 flex items-center gap-2">
            💻 示例预览
          </h4>
          <div className="relative overflow-hidden rounded-xl border border-neutral-200/50 dark:border-neutral-700/50">
            <CodeHighlight 
              code={cardContent.truncatedExample}
              language="javascript"
              isPreview={true}
              showLineNumbers={false}
            />
            {cardContent.remainingLines > 0 && (
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-white dark:from-neutral-900 to-transparent p-3 text-center">
                <span className="text-xs text-neutral-500 dark:text-neutral-400 font-medium bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-neutral-200/50 dark:border-neutral-700/50">
                  ✨ 还有 {cardContent.remainingLines} 行代码
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 专业提示区域 */}
        {item.notes && (
          <div className="relative">
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 to-amber-900/20 border border-orange-200/50 dark:border-orange-700/50 p-4 rounded-xl">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 animate-pulse"></div>
                <p className="text-sm text-orange-700 dark:text-orange-300 leading-relaxed font-medium">
                  {item.notes}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {/* 简洁操作区域 */}
        <div className="pt-4 border-t border-neutral-200/50 dark:border-neutral-700/50">
          <div className="flex items-center justify-center gap-2 text-neutral-500 dark:text-neutral-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            <BookOpen className="w-4 h-4" />
            <span className="text-sm font-medium">点击查看完整文档</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ApiCard.displayName = 'ApiCard';

// 懒加载的Tab内容组件
const LazyTabContent = memo<{
  tabKey: string;
  item: ApiItem;
  copyToClipboard: (text: string) => void;
  secondaryTabStates: Record<string, number>;
  setSecondaryTabStates: React.Dispatch<React.SetStateAction<Record<string, number>>>;
}>(({ tabKey, item, copyToClipboard, secondaryTabStates, setSecondaryTabStates }) => {
  
  // 渲染扩展Tab内容
  const renderExtensionTabs = useCallback((item: ApiItem) => {
    const extensionTabs = item.extensionTabs;
    
    if (!extensionTabs || !extensionTabs.tabs.length) {
      return (
        <div className="text-center text-gray-500 py-12">
          <Text>扩展内容正在完善中...</Text>
        </div>
      );
    }
    
    return (
      <div className="space-y-6">
        <div>
          <Title level={4}>{extensionTabs.title}</Title>
          <Paragraph>{extensionTabs.description}</Paragraph>
        </div>
        
        <Tabs
          type="card"
          items={extensionTabs.tabs.map(tab => ({
            key: tab.key,
            label: tab.title,
            children: (
              <div className="p-4">
                {tab.content && typeof tab.content === 'string' ? (
                  <div className="prose max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeHighlight, rehypeRaw]}
                      components={{
                        code: ({ className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          const language = match ? match[1] : '';
                          
                          if (!className?.includes('language-')) {
                            return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props}>{children}</code>;
                          }
                          
                          return (
                            <CodeHighlight
                              code={String(children).replace(/\n$/, '')}
                              language={language}
                              title=""
                              onCopy={() => copyToClipboard(String(children).replace(/\n$/, ''))}
                            />
                          );
                        },
                        pre: ({ children }) => <>{children}</>,
                      }}
                    >
                      {tab.content}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <Text>内容加载中...</Text>
                  </div>
                )}
              </div>
            )
          }))}
        />
      </div>
    );
  }, [copyToClipboard]);

  // 根据tabKey渲染对应内容
  const renderTabContent = useCallback((tabKey: string, item: ApiItem, copyToClipboard: (text: string) => void) => {
    switch (tabKey) {
      case 'basic':
        return renderBasicInfo(item, copyToClipboard);
      case 'business':
        return renderBusinessScenarios(item, copyToClipboard);
      case 'implementation':
        return renderImplementation(item);
      case 'interview':
        return renderInterviewQuestions(item, copyToClipboard, secondaryTabStates, setSecondaryTabStates);
      case 'questions':
        return renderCommonQuestions(item, copyToClipboard, secondaryTabStates, setSecondaryTabStates);
      case 'archaeology':
        return renderKnowledgeArchaeology(item);
      case 'performance':
        return renderPerformanceOptimization(item, secondaryTabStates, setSecondaryTabStates);
      case 'learning':
        return renderLearningPath(item);
      case 'migration':
        return renderVersionMigration(item);
      case 'ecosystem':
        return renderEcosystemTools(item);
      case 'projects':
        return renderRealWorldProjects(item, copyToClipboard);
      case 'debugging':
        return <DebuggingTipsComponent item={item} copyToClipboard={copyToClipboard} />;
      case 'essence':
        return renderEssenceInsights(item);
      case 'extensionTabs':
        return renderExtensionTabs(item);
      default:
        return <div>Tab content not found</div>;
    }
  }, [secondaryTabStates, setSecondaryTabStates]);

  return (
    <Suspense fallback={<div className="flex items-center justify-center p-8"><Spin size="large" /></div>}>
      {renderTabContent(tabKey, item, copyToClipboard)}
    </Suspense>
  );
});

LazyTabContent.displayName = 'LazyTabContent';

const CheatSheet: React.FC<CheatSheetProps> = ({ 
  title,
  subtitle,
  apiData,
  themeColor = 'default',
  onThemeChange,
  selectedCategory = 'All',
  expandAll = false 
}) => {
  // 基础状态
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'name' | 'difficulty' | 'category'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [visibleCount, setVisibleCount] = useState(20);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandAllState, setExpandAllState] = useState(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);
  
  // 搜索选项状态
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    title: true,
    description: true,
    syntax: true,
    example: true,
    notes: true,
    beginnerGuide: true,
    businessScenarios: true,
    implementation: true,
    category: true
  });
  
  // 收藏状态 - 从localStorage读取初始值
  const [favorites, setFavorites] = useState<string[]>(() => {
    try {
      const saved = localStorage.getItem('react-cheatsheet-favorites');
      return saved ? JSON.parse(saved) : [];
    } catch {
      return [];
    }
  });
  
  // 分类选择状态
  const [currentSelectedCategory, setSelectedCategory] = useState(selectedCategory);
  
  // Drawer相关状态
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ApiItem | null>(null);
  const [activeTab, setActiveTab] = useState('basic');
  
  // Tab管理状态
  const [userRole, setUserRole] = useState<'learner' | 'interviewer' | 'developer' | 'all'>('all');
  const [customTabsEnabled, setCustomTabsEnabled] = useState(false);
  const [visibleTabs, setVisibleTabs] = useState(new Set(RECOMMENDED_TABS.all));
  
  // 新增：子Tab状态管理
  const [subTabStates, setSubTabStates] = useState<Record<string, Record<string, string>>>({});
  const [secondaryTabStates, setSecondaryTabStates] = useState<Record<string, number>>({});

  // 获取特定组件的子Tab状态
  const getSubTabState = (itemId: string, tabKey: string, defaultValue: string = 'overview') => {
    return subTabStates[itemId]?.[tabKey] || defaultValue;
  };

  // 设置特定组件的子Tab状态
  const setSubTabState = (itemId: string, tabKey: string, value: string) => {
    setSubTabStates(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [tabKey]: value
      }
    }));
  };

  // 添加路由导航
  const navigate = useNavigate();

  // 记忆化计算主题配置
  const currentTheme = useMemo(() => {
    const themes = {
      vue: { borderColor: 'border-l-green-500' },
      react: { borderColor: 'border-l-blue-500' },
      nextjs: { borderColor: 'border-l-purple-500' },
      typescript: { borderColor: 'border-l-indigo-500' },
      ecma: { borderColor: 'border-l-yellow-500' },
      default: { borderColor: 'border-l-gray-500' }
    };
    return themes[themeColor] || themes.default;
  }, [themeColor]);

  // 记忆化计算过滤数据
  const filteredData = useMemo(() => {
    let filtered = apiData;

    // 版本过滤（支持ECMAScript和React）
    if (currentSelectedCategory !== 'All') {
      filtered = filtered.filter(item => {
        const version = item.version || '';

        // ECMAScript版本过滤
        if (themeColor === 'ecma') {
          if (currentSelectedCategory === 'ES6') return version.includes('ES6') || version.includes('ES2015');
          if (currentSelectedCategory === 'ES2017') return version.includes('ES2017') || version.includes('ES8');
          if (currentSelectedCategory === 'ES2018') return version.includes('ES2018') || version.includes('ES9');
          if (currentSelectedCategory === 'ES2019') return version.includes('ES2019') || version.includes('ES10');
          if (currentSelectedCategory === 'ES2020') return version.includes('ES2020') || version.includes('ES11');
          if (currentSelectedCategory === 'ES2021') return version.includes('ES2021') || version.includes('ES12');
          if (currentSelectedCategory === 'ES2022') return version.includes('ES2022') || version.includes('ES13');
          if (currentSelectedCategory === 'ES2023') return version.includes('ES2023') || version.includes('ES14');
          if (currentSelectedCategory === 'ES2024') return version.includes('ES2024') || version.includes('ES15');
          return currentSelectedCategory === 'Other';
        }

        // React版本过滤
        if (themeColor === 'react') {
          if (currentSelectedCategory === 'React 0.x') return version.includes('React 0.') || version.includes('0.');
          if (currentSelectedCategory === 'React 15') return version.includes('React 15') || version.includes('15.');
          if (currentSelectedCategory === 'React 16.0') return version.includes('React 16.0') || version.includes('16.0');
          if (currentSelectedCategory === 'React 16.3') return version.includes('React 16.3') || version.includes('16.3');
          if (currentSelectedCategory === 'React 16.6') return version.includes('React 16.6') || version.includes('16.6');
          if (currentSelectedCategory === 'React 16.8') return version.includes('React 16.8') || version.includes('16.8');
          if (currentSelectedCategory === 'React 16.x') return version.includes('React 16') && !version.includes('16.0') && !version.includes('16.3') && !version.includes('16.6') && !version.includes('16.8');
          if (currentSelectedCategory === 'React 18.0') return version.includes('React 18.0') || version.includes('18.0');
          if (currentSelectedCategory === 'React 18.2') return version.includes('React 18.2') || version.includes('18.2');
          if (currentSelectedCategory === 'React 18.x') return version.includes('React 18') && !version.includes('18.0') && !version.includes('18.2');
          if (currentSelectedCategory === 'React 19') return version.includes('React 19') || version.includes('19.');
          if (currentSelectedCategory === 'RSC') return version.includes('Server Components');
          return currentSelectedCategory === 'Other';
        }

        // 其他框架使用category过滤
        return item.category === currentSelectedCategory;
      });
    }

    // 搜索过滤
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(item => {
        return (
          (searchOptions.title && item.title?.toLowerCase().includes(searchLower)) ||
          (searchOptions.description && item.description?.toLowerCase().includes(searchLower)) ||
          (searchOptions.syntax && item.syntax?.toLowerCase().includes(searchLower)) ||
          (searchOptions.example && item.example?.toLowerCase().includes(searchLower)) ||
          (searchOptions.notes && item.notes?.toLowerCase().includes(searchLower)) ||
          (searchOptions.category && item.category?.toLowerCase().includes(searchLower))
        );
      });
    }

    // 🌟 收藏排序：收藏的项目排在最前面
    return filtered.sort((a, b) => {
      const aIsFavorite = favorites.includes(a.id);
      const bIsFavorite = favorites.includes(b.id);

      // 如果一个是收藏，一个不是，收藏的排在前面
      if (aIsFavorite && !bIsFavorite) return -1;
      if (!aIsFavorite && bIsFavorite) return 1;

      // 如果都是收藏或都不是收藏，保持原有顺序
      return 0;
    });
  }, [apiData, currentSelectedCategory, searchTerm, searchOptions, favorites, themeColor]);

  // 记忆化计算版本列表（支持ECMAScript和React）
  const categories = useMemo(() => {
    const versionSet = new Set(apiData.map(item => {
      const version = item.version || '';

      // ECMAScript版本处理
      if (themeColor === 'ecma') {
        if (version.includes('ES6') || version.includes('ES2015')) return 'ES6';
        if (version.includes('ES2017') || version.includes('ES8')) return 'ES2017';
        if (version.includes('ES2018') || version.includes('ES9')) return 'ES2018';
        if (version.includes('ES2019') || version.includes('ES10')) return 'ES2019';
        if (version.includes('ES2020') || version.includes('ES11')) return 'ES2020';
        if (version.includes('ES2021') || version.includes('ES12')) return 'ES2021';
        if (version.includes('ES2022') || version.includes('ES13')) return 'ES2022';
        if (version.includes('ES2023') || version.includes('ES14')) return 'ES2023';
        if (version.includes('ES2024') || version.includes('ES15')) return 'ES2024';
        return 'Other';
      }

      // React版本处理
      if (themeColor === 'react') {
        if (version.includes('React 0.') || version.includes('0.')) return 'React 0.x';
        if (version.includes('React 15') || version.includes('15.')) return 'React 15';
        if (version.includes('React 16.0') || version.includes('16.0')) return 'React 16.0';
        if (version.includes('React 16.3') || version.includes('16.3')) return 'React 16.3';
        if (version.includes('React 16.6') || version.includes('16.6')) return 'React 16.6';
        if (version.includes('React 16.8') || version.includes('16.8')) return 'React 16.8';
        if (version.includes('React 16') || version.includes('16.')) return 'React 16.x';
        if (version.includes('React 18.0') || version.includes('18.0')) return 'React 18.0';
        if (version.includes('React 18.2') || version.includes('18.2')) return 'React 18.2';
        if (version.includes('React 18') || version.includes('18.')) return 'React 18.x';
        if (version.includes('React 19') || version.includes('19.')) return 'React 19';
        if (version.includes('Server Components')) return 'RSC';
        return 'Other';
      }

      // 其他框架使用原有的category
      return item.category || 'Other';
    }));

    // 按版本顺序排序
    let sortedVersions;
    if (themeColor === 'ecma') {
      const order = ['ES6', 'ES2017', 'ES2018', 'ES2019', 'ES2020', 'ES2021', 'ES2022', 'ES2023', 'ES2024', 'Other'];
      sortedVersions = Array.from(versionSet).sort((a, b) => order.indexOf(a) - order.indexOf(b));
    } else if (themeColor === 'react') {
      const order = ['React 0.x', 'React 15', 'React 16.0', 'React 16.3', 'React 16.6', 'React 16.8', 'React 16.x', 'React 18.0', 'React 18.2', 'React 18.x', 'React 19', 'RSC', 'Other'];
      sortedVersions = Array.from(versionSet).sort((a, b) => order.indexOf(a) - order.indexOf(b));
    } else {
      sortedVersions = Array.from(versionSet).sort();
    }

    return ['All', ...sortedVersions];
  }, [apiData, themeColor]);

  // 记忆化回调函数
  const toggleFavorite = useCallback((id: string) => {
    setFavorites(prev => {
      const newFavorites = prev.includes(id)
        ? prev.filter(fav => fav !== id)
        : [...prev, id];

      // 保存到localStorage
      try {
        localStorage.setItem('react-cheatsheet-favorites', JSON.stringify(newFavorites));
      } catch (error) {
        console.warn('Failed to save favorites to localStorage:', error);
      }

      return newFavorites;
    });
  }, []);

  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text);
  }, []);

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 border-green-300';
      case 'medium': return 'text-yellow-600 border-yellow-300';
      case 'hard': return 'text-red-600 border-red-300';
      default: return 'text-gray-600 border-gray-300';
    }
  };

  const handleSearchOptionChange = (option: keyof SearchOptions, checked: boolean) => {
    setSearchOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  };

  const getActiveSearchCount = () => {
    return Object.values(searchOptions).filter(Boolean).length;
  };

  const setAllSearchOptions = (value: boolean) => {
    const newOptions = Object.keys(searchOptions).reduce((acc, key) => {
      acc[key as keyof SearchOptions] = value;
      return acc;
    }, {} as SearchOptions);
    setSearchOptions(newOptions);
  };

  const handleCardClick = useCallback((item: ApiItem) => {
    setSelectedItem(item);
    setDrawerVisible(true);
    setActiveTab('basic');
  }, []);

  const handleDrawerClose = useCallback(() => {
    setDrawerVisible(false);
    setSelectedItem(null);
    // 重置所有二级Tab状态
    setSecondaryTabStates({
      interviewQuestions: 0,
      commonQuestions: 0,
      businessScenarios: 0,
      performanceOptimization: 0,
    });
  }, []);

  // 记忆化获取可用的Tab列表
  const getAvailableTabs = useCallback((item: ApiItem) => {
    const availableTabs = new Set<string>();
    
    // 🆕 9个核心Tab固定显示（不管是否有数据）
    availableTabs.add('basic');       // 基本信息
    availableTabs.add('business');    // 业务场景  
    availableTabs.add('implementation'); // 原理解析
    availableTabs.add('interview');   // 面试准备
    availableTabs.add('questions');   // 常见问题
    availableTabs.add('archaeology'); // 知识考古
    availableTabs.add('performance'); // 性能优化
    availableTabs.add('debugging');   // 调试技巧
    availableTabs.add('essence');     // 本质洞察
    
    // 其他高级Tab（根据数据存在性显示）
    if (item.learningPath) availableTabs.add('learning');
    if (item.versionMigration) availableTabs.add('migration');
    if (item.ecosystemTools) availableTabs.add('ecosystem');
    if (item.realWorldProjects) availableTabs.add('projects');
    
    // 🆕 自定义扩展Tab（根据数据存在性）
    if (item.extensionTabs && item.extensionTabs.tabs.length > 0) {
      availableTabs.add('extensionTabs');
    }
    
    return Array.from(availableTabs);
  }, []);

  // 记忆化获取显示的Tab列表
  const getVisibleTabs = useCallback((item: ApiItem) => {
    const available = new Set(getAvailableTabs(item));
    const userSelected = customTabsEnabled ? visibleTabs : new Set(RECOMMENDED_TABS[userRole]);
    return Array.from(available).filter(tab => userSelected.has(tab));
  }, [getAvailableTabs, customTabsEnabled, visibleTabs, userRole]);

  // 设置用户角色
  const setUserRoleAndTabs = (role: typeof userRole) => {
    setUserRole(role);
    setVisibleTabs(new Set(RECOMMENDED_TABS[role]));
    setCustomTabsEnabled(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-50 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950">
      <div className="w-full max-w-none px-6 py-6 main-content">
        {/* 顶部专业切换器 */}
        <div className="flex justify-end mb-6">
          <Dropdown
            overlay={
              <Menu className="min-w-[200px] rounded-xl shadow-xl border-0 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm">
                {[
                  { key: 'vue', label: 'Vue.js', version: '3.5', icon: '🟢', description: 'Progressive JS Framework' },
                  { key: 'react', label: 'React', version: '18', icon: '🔵', description: 'A JavaScript library' },
                  { key: 'ecma', label: 'ECMAScript', version: 'ES2024', icon: '🟡', description: 'Modern JavaScript Features' },
                  { key: 'nextjs', label: 'Next.js', version: '14', icon: '⚡', description: 'The React Framework' },
                  { key: 'typescript', label: 'TypeScript', version: '5.3', icon: '🔷', description: 'JavaScript with Types' },
                  { key: 'default', label: 'General', version: '', icon: '⚪', description: 'Framework agnostic' }
                ].map(({ key, label, version, icon, description }) => (
                  <Menu.Item 
                    key={key}
                    onClick={() => {
                      // 如果是当前页面，不做跳转
                      if (key === themeColor) return;
                      
                      // 根据选择的框架进行路由跳转
                      if (key === 'vue') {
                        navigate('/vue');
                      } else if (key === 'react') {
                        navigate('/react');
                      } else if (key === 'ecma') {
                        navigate('/ecma');
                      } else if (key === 'nextjs') {
                        navigate('/nextjs');
                      } else if (key === 'typescript') {
                        navigate('/typescript');
                      } else {
                        navigate('/');
                      }
                      
                      // 如果有回调函数，也调用它
                      onThemeChange?.(key as 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default');
                    }}
                    className={`px-4 py-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors ${
                      themeColor === key ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <span className="text-lg mt-0.5">{icon}</span>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className={`font-medium ${
                            themeColor === key ? 'text-blue-600 dark:text-blue-400' : 'text-neutral-900 dark:text-neutral-100'
                          }`}>
                            {label}
                          </span>
                          {version && (
                            <span className="text-xs text-neutral-500 dark:text-neutral-400">v{version}</span>
                          )}
                        </div>
                        <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                          {description}
                        </div>
                      </div>
                      {themeColor === key && (
                        <Check className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-1" />
                      )}
                    </div>
                  </Menu.Item>
                ))}
              </Menu>
            }
            trigger={['click']}
            placement="bottomRight"
          >
            <button className="flex items-center gap-3 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 rounded-xl px-4 py-2.5 shadow-sm hover:shadow-md transition-all duration-300 hover:border-neutral-300 dark:hover:border-neutral-600">
              <div className="flex items-center gap-2">
                <span className="text-lg">
                  {themeColor === 'vue' ? '🟢' :
                   themeColor === 'react' ? '🔵' :
                   themeColor === 'ecma' ? '🟡' :
                   themeColor === 'nextjs' ? '⚡' :
                   themeColor === 'typescript' ? '🔷' :
                   '⚪'}
                </span>
                <span className="font-medium text-neutral-700 dark:text-neutral-300">
                  {themeColor === 'vue' ? 'Vue 3.5' :
                   themeColor === 'react' ? 'React 18' :
                   themeColor === 'ecma' ? 'ES2024' :
                   themeColor === 'nextjs' ? 'Next.js 14' :
                   themeColor === 'typescript' ? 'TypeScript 5.3' :
                   'Latest'}
                </span>
              </div>
              <svg className="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </Dropdown>
        </div>

        {/* 专业Header区域 - 收紧间距 */}
        <div className="text-center mb-10 relative">
          {/* 简洁背景效果 - 适配主题色 */}
          <div className="absolute inset-0 -top-4">
            <div className={`absolute inset-0 blur-3xl ${
              themeColor === 'vue' ? 'bg-gradient-to-r from-green-500/3 via-emerald-500/3 to-blue-500/3' :
              themeColor === 'react' ? 'bg-gradient-to-r from-blue-500/3 via-purple-500/3 to-cyan-500/3' :
              themeColor === 'ecma' ? 'bg-gradient-to-r from-yellow-500/3 via-amber-500/3 to-orange-500/3' :
              themeColor === 'nextjs' ? 'bg-gradient-to-r from-purple-500/3 via-indigo-500/3 to-cyan-500/3' :
              themeColor === 'typescript' ? 'bg-gradient-to-r from-gray-500/3 via-slate-500/3 to-blue-500/3' :
              'bg-gradient-to-r from-gray-500/3 via-slate-500/3 to-blue-500/3'
            }`}></div>
          </div>
          
          <div className="relative z-10">
            {/* 版本标识 - 主题色适配 */}
            <div className="flex items-center justify-center mb-4">
              <div className={`inline-flex items-center gap-3 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-sm border rounded-full px-5 py-2 shadow-sm hover:shadow-md transition-all duration-300 ${
                themeColor === 'vue' ? 'border-green-500/30 hover:border-green-500/50' :
                themeColor === 'react' ? 'border-blue-500/30 hover:border-blue-500/50' :
                themeColor === 'ecma' ? 'border-yellow-500/30 hover:border-yellow-500/50' :
                themeColor === 'nextjs' ? 'border-purple-500/30 hover:border-purple-500/50' :
                themeColor === 'typescript' ? 'border-gray-500/30 hover:border-gray-500/50' :
                'border-gray-500/30 hover:border-gray-500/50'
              }`}>
                <div className="relative">
                  <div className={`w-2 h-2 rounded-full animate-pulse ${
                    themeColor === 'vue' ? 'bg-green-500' :
                    themeColor === 'react' ? 'bg-blue-500' :
                    themeColor === 'ecma' ? 'bg-yellow-500' :
                    themeColor === 'nextjs' ? 'bg-purple-500' :
                    themeColor === 'typescript' ? 'bg-gray-500' :
                    'bg-gray-500'
                  }`}></div>
                  <div className={`absolute inset-0 w-2 h-2 rounded-full animate-ping ${
                    themeColor === 'vue' ? 'bg-green-500/30' :
                    themeColor === 'react' ? 'bg-blue-500/30' :
                    themeColor === 'ecma' ? 'bg-yellow-500/30' :
                    themeColor === 'nextjs' ? 'bg-purple-500/30' :
                    themeColor === 'typescript' ? 'bg-gray-500/30' :
                    'bg-gray-500/30'
                  }`}></div>
                </div>
                <span className={`text-sm font-semibold tracking-wide ${
                  themeColor === 'vue' ? 'text-green-700 dark:text-green-300' :
                  themeColor === 'react' ? 'text-blue-700 dark:text-blue-300' :
                  themeColor === 'ecma' ? 'text-yellow-700 dark:text-yellow-300' :
                  themeColor === 'nextjs' ? 'text-purple-700 dark:text-purple-300' :
                  themeColor === 'typescript' ? 'text-gray-700 dark:text-gray-300' :
                  'text-gray-700 dark:text-gray-300'
                }`}>
                  {themeColor === 'vue' ? 'Vue 3.5' :
                   themeColor === 'react' ? 'React 18' :
                   themeColor === 'ecma' ? 'ES2024' :
                   themeColor === 'nextjs' ? 'Next.js 14' :
                   themeColor === 'typescript' ? 'TypeScript 5.3' :
                   'Latest'}
                </span>
                <div className="w-px h-3 bg-neutral-300 dark:bg-neutral-600"></div>
                <span className="text-xs font-medium text-neutral-600 dark:text-neutral-400">Latest</span>
              </div>
            </div>
            
            {/* 主标题 - 使用props */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight mb-3">
              <span className={`bg-gradient-to-r bg-clip-text text-transparent ${
                themeColor === 'vue' ? 'from-green-600 via-emerald-600 to-blue-600 dark:from-green-400 dark:via-emerald-400 dark:to-blue-400' :
                themeColor === 'react' ? 'from-blue-600 via-purple-600 to-cyan-600 dark:from-blue-400 dark:via-purple-400 dark:to-cyan-400' :
                themeColor === 'ecma' ? 'from-yellow-600 via-amber-600 to-orange-600 dark:from-yellow-400 dark:via-amber-400 dark:to-orange-400' :
                themeColor === 'nextjs' ? 'from-purple-600 via-indigo-600 to-cyan-600 dark:from-purple-400 dark:via-indigo-400 dark:to-cyan-400' :
                themeColor === 'typescript' ? 'from-gray-600 via-slate-600 to-blue-600 dark:from-gray-400 dark:via-slate-400 dark:to-blue-400' :
                'from-gray-700 via-slate-600 to-blue-600 dark:from-gray-300 dark:via-slate-400 dark:to-blue-400'
              }`}>
                {title}
              </span>
            </h1>
            
            {/* 副标题 - 使用props */}
            <div className="text-base md:text-lg text-neutral-600 dark:text-neutral-400 font-medium leading-relaxed max-w-2xl mx-auto mb-5">
              {subtitle}
            </div>
            
            {/* 简洁分隔线 - 主题色 */}
            <div className="flex items-center justify-center mb-6">
              <div className="flex items-center gap-2">
                <div className="w-12 h-px bg-gradient-to-r from-transparent via-neutral-300 dark:via-neutral-600 to-transparent"></div>
                <div className={`w-1.5 h-1.5 rounded-full ${
                  themeColor === 'vue' ? 'bg-green-500' :
                  themeColor === 'react' ? 'bg-blue-500' :
                  themeColor === 'ecma' ? 'bg-yellow-500' :
                  themeColor === 'nextjs' ? 'bg-purple-500' :
                  themeColor === 'typescript' ? 'bg-gray-500' :
                  'bg-gray-500'
                }`}></div>
                <div className={`w-6 h-px ${
                  themeColor === 'vue' ? 'bg-green-500/50' :
                  themeColor === 'react' ? 'bg-blue-500/50' :
                  themeColor === 'ecma' ? 'bg-yellow-500/50' :
                  themeColor === 'nextjs' ? 'bg-purple-500/50' :
                  themeColor === 'typescript' ? 'bg-gray-500/50' :
                  'bg-gray-500/50'
                }`}></div>
                <div className={`w-1 h-1 rounded-full ${
                  themeColor === 'vue' ? 'bg-emerald-500' :
                  themeColor === 'react' ? 'bg-purple-500' :
                  themeColor === 'ecma' ? 'bg-amber-500' :
                  themeColor === 'nextjs' ? 'bg-indigo-500' :
                  themeColor === 'typescript' ? 'bg-slate-500' :
                  'bg-slate-500'
                }`}></div>
                <div className={`w-6 h-px ${
                  themeColor === 'vue' ? 'bg-emerald-500/50' :
                  themeColor === 'react' ? 'bg-purple-500/50' :
                  themeColor === 'ecma' ? 'bg-amber-500/50' :
                  themeColor === 'nextjs' ? 'bg-indigo-500/50' :
                  themeColor === 'typescript' ? 'bg-slate-500/50' :
                  'bg-slate-500/50'
                }`}></div>
                <div className={`w-1.5 h-1.5 rounded-full ${
                  themeColor === 'vue' ? 'bg-blue-500' :
                  themeColor === 'react' ? 'bg-cyan-500' :
                  themeColor === 'ecma' ? 'bg-orange-500' :
                  themeColor === 'nextjs' ? 'bg-purple-500' :
                  themeColor === 'typescript' ? 'bg-gray-500' :
                  'bg-blue-500'
                }`}></div>
                <div className="w-12 h-px bg-gradient-to-l from-transparent via-neutral-300 dark:via-neutral-600 to-transparent"></div>
              </div>
            </div>
            
            {/* 专业特性标签 */}
            <div className="flex flex-wrap justify-center items-center gap-6">
              <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <div className="relative">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-1.5 h-1.5 bg-orange-400/30 rounded-full animate-ping"></div>
                </div>
                <span className="font-medium group-hover:text-orange-500 transition-colors">Live Updates</span>
              </div>
              
              <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <Star className="w-3.5 h-3.5 text-yellow-500 group-hover:scale-110 transition-transform" />
                <span className="font-medium group-hover:text-yellow-600 transition-colors">Bookmarkable</span>
              </div>
              
              <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <Copy className="w-3.5 h-3.5 text-blue-500 group-hover:scale-110 transition-transform" />
                <span className="font-medium group-hover:text-blue-600 transition-colors">One-Click Copy</span>
              </div>
              
              <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <SettingOutlined className="w-3.5 h-3.5 text-purple-500 group-hover:scale-110 transition-transform" />
                <span className="font-medium group-hover:text-purple-600 transition-colors">Smart Tabs</span>
              </div>
            </div>
          </div>
        </div>

        {/* 现代化搜索区域 - 收紧间距 */}
        <div className="mb-6 space-y-4 max-w-5xl mx-auto">
          <div className="relative group">
            {/* 搜索框背景光晕 */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            
            <div className="relative bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 p-1.5 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
                <Input
                  placeholder="搜索 API、函数、组件..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 pr-32 h-14 text-lg bg-transparent border-0 focus:ring-0 focus:outline-none"
                />
                
                {/* 搜索选项按钮 */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 px-4 bg-neutral-50/80 hover:bg-neutral-100/80 dark:bg-neutral-800/80 dark:hover:bg-neutral-700/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 rounded-xl transition-all duration-300 hover:scale-105"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      <span className="text-sm font-medium">搜索选项</span>
                      {getActiveSearchCount() > 0 && (
                        <div className="ml-2 bg-blue-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getActiveSearchCount()}
                        </div>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-96 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-700/50 shadow-xl rounded-2xl" align="end">
                    <div className="space-y-6 p-2">
                      <div className="font-semibold text-lg text-neutral-900 dark:text-neutral-100">Search Options</div>
                      <div className="grid grid-cols-2 gap-4">
                        {[
                          { key: 'title', label: 'Title' },
                          { key: 'description', label: 'Description' },
                          { key: 'syntax', label: 'Syntax' },
                          { key: 'example', label: 'Code Examples' },
                          { key: 'notes', label: 'Notes' },
                          { key: 'category', label: 'Category' },
                        ].map(({ key, label }) => (
                          <div key={key} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors group">
                            <Checkbox
                              id={key}
                              checked={searchOptions[key as keyof SearchOptions]}
                              onCheckedChange={(checked) => 
                                handleSearchOptionChange(key as keyof SearchOptions, checked as boolean)
                              }
                              className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                            />
                            <label
                              htmlFor={key}
                              className="text-sm font-medium leading-none cursor-pointer group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                            >
                              {label}
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="pt-4 border-t border-neutral-200/50 dark:border-neutral-700/50">
                        <div className="flex gap-3">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setAllSearchOptions(true)}
                            className="flex-1 bg-neutral-50 hover:bg-neutral-100 text-neutral-700 border-neutral-200 rounded-lg"
                          >
                            Select All
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setAllSearchOptions(false)}
                            className="flex-1 bg-neutral-50 hover:bg-neutral-100 text-neutral-700 border-neutral-200 rounded-lg"
                          >
                            Clear All
                          </Button>
                        </div>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          
          {/* 版本标签 */}
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map(category => {
              // 获取版本显示名称、描述和颜色
              const getVersionInfo = (version: string) => {
                // ECMAScript版本信息
                if (themeColor === 'ecma') {
                  switch (version) {
                    case 'All': return { name: 'All', desc: '所有版本', color: 'neutral' };
                    case 'ES6': return { name: 'ES6', desc: 'ES2015 - 现代JavaScript基础', color: 'blue' };
                    case 'ES2017': return { name: 'ES2017', desc: 'ES8 - 异步编程增强', color: 'green' };
                    case 'ES2018': return { name: 'ES2018', desc: 'ES9 - 异步迭代器', color: 'purple' };
                    case 'ES2019': return { name: 'ES2019', desc: 'ES10 - 数组和对象增强', color: 'orange' };
                    case 'ES2020': return { name: 'ES2020', desc: 'ES11 - 可选链和空值合并', color: 'red' };
                    case 'ES2021': return { name: 'ES2021', desc: 'ES12 - 逻辑赋值运算符', color: 'pink' };
                    case 'ES2022': return { name: 'ES2022', desc: 'ES13 - 类字段和顶层await', color: 'indigo' };
                    case 'ES2023': return { name: 'ES2023', desc: 'ES14 - 数组方法增强', color: 'teal' };
                    case 'ES2024': return { name: 'ES2024', desc: 'ES15 - 最新特性', color: 'emerald' };
                    default: return { name: version, desc: '', color: 'gray' };
                  }
                }

                // React版本信息
                if (themeColor === 'react') {
                  switch (version) {
                    case 'All': return { name: 'All', desc: '所有版本', color: 'neutral' };
                    case 'React 0.x': return { name: 'React 0.x', desc: '早期版本 - 基础组件系统', color: 'gray' };
                    case 'React 15': return { name: 'React 15', desc: '稳定版本 - PureComponent', color: 'blue' };
                    case 'React 16.0': return { name: 'React 16.0', desc: 'Fiber架构 - 错误边界', color: 'green' };
                    case 'React 16.3': return { name: 'React 16.3', desc: 'Context API - createRef', color: 'purple' };
                    case 'React 16.6': return { name: 'React 16.6', desc: 'Suspense - React.memo', color: 'orange' };
                    case 'React 16.8': return { name: 'React 16.8', desc: 'Hooks革命 - useState/useEffect', color: 'red' };
                    case 'React 16.x': return { name: 'React 16.x', desc: '其他16.x版本特性', color: 'pink' };
                    case 'React 18.0': return { name: 'React 18.0', desc: '并发特性 - 自动批处理', color: 'indigo' };
                    case 'React 18.2': return { name: 'React 18.2', desc: '资源预加载 - preinit/preload', color: 'teal' };
                    case 'React 18.x': return { name: 'React 18.x', desc: '其他18.x版本特性', color: 'emerald' };
                    case 'React 19': return { name: 'React 19', desc: '最新特性 - useActionState', color: 'cyan' };
                    case 'RSC': return { name: 'RSC', desc: 'React Server Components', color: 'violet' };
                    default: return { name: version, desc: '', color: 'gray' };
                  }
                }

                // 其他框架的默认处理
                return { name: version, desc: '', color: 'neutral' };
              };

              // 计算该版本的特性数量
              const getVersionCount = (version: string) => {
                if (version === 'All') return apiData.length;
                return apiData.filter(item => {
                  const itemVersion = item.version || '';

                  // ECMAScript版本计数
                  if (themeColor === 'ecma') {
                    if (version === 'ES6') return itemVersion.includes('ES6') || itemVersion.includes('ES2015');
                    if (version === 'ES2017') return itemVersion.includes('ES2017') || itemVersion.includes('ES8');
                    if (version === 'ES2018') return itemVersion.includes('ES2018') || itemVersion.includes('ES9');
                    if (version === 'ES2019') return itemVersion.includes('ES2019') || itemVersion.includes('ES10');
                    if (version === 'ES2020') return itemVersion.includes('ES2020') || itemVersion.includes('ES11');
                    if (version === 'ES2021') return itemVersion.includes('ES2021') || itemVersion.includes('ES12');
                    if (version === 'ES2022') return itemVersion.includes('ES2022') || itemVersion.includes('ES13');
                    if (version === 'ES2023') return itemVersion.includes('ES2023') || itemVersion.includes('ES14');
                    if (version === 'ES2024') return itemVersion.includes('ES2024') || itemVersion.includes('ES15');
                    return false;
                  }

                  // React版本计数
                  if (themeColor === 'react') {
                    if (version === 'React 0.x') return itemVersion.includes('React 0.') || itemVersion.includes('0.');
                    if (version === 'React 15') return itemVersion.includes('React 15') || itemVersion.includes('15.');
                    if (version === 'React 16.0') return itemVersion.includes('React 16.0') || itemVersion.includes('16.0');
                    if (version === 'React 16.3') return itemVersion.includes('React 16.3') || itemVersion.includes('16.3');
                    if (version === 'React 16.6') return itemVersion.includes('React 16.6') || itemVersion.includes('16.6');
                    if (version === 'React 16.8') return itemVersion.includes('React 16.8') || itemVersion.includes('16.8');
                    if (version === 'React 16.x') return itemVersion.includes('React 16') && !itemVersion.includes('16.0') && !itemVersion.includes('16.3') && !itemVersion.includes('16.6') && !itemVersion.includes('16.8');
                    if (version === 'React 18.0') return itemVersion.includes('React 18.0') || itemVersion.includes('18.0');
                    if (version === 'React 18.2') return itemVersion.includes('React 18.2') || itemVersion.includes('18.2');
                    if (version === 'React 18.x') return itemVersion.includes('React 18') && !itemVersion.includes('18.0') && !itemVersion.includes('18.2');
                    if (version === 'React 19') return itemVersion.includes('React 19') || itemVersion.includes('19.');
                    if (version === 'RSC') return itemVersion.includes('Server Components');
                    return false;
                  }

                  // 其他框架使用category匹配
                  return item.category === version;
                }).length;
              };

              const versionInfo = getVersionInfo(category);
              const count = getVersionCount(category);

              // 获取颜色样式
              const getColorStyles = (color: string, isActive: boolean) => {
                const colorMap = {
                  neutral: isActive
                    ? 'bg-neutral-900 dark:bg-neutral-100 text-white dark:text-neutral-900 border-neutral-900 dark:border-neutral-100'
                    : 'bg-white dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-neutral-300 dark:border-neutral-600 hover:border-neutral-900 dark:hover:border-neutral-300',
                  blue: isActive
                    ? 'bg-blue-600 text-white border-blue-600 shadow-blue-200'
                    : 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700 hover:border-blue-600 hover:bg-blue-100',
                  green: isActive
                    ? 'bg-green-600 text-white border-green-600 shadow-green-200'
                    : 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 hover:border-green-600 hover:bg-green-100',
                  purple: isActive
                    ? 'bg-purple-600 text-white border-purple-600 shadow-purple-200'
                    : 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700 hover:border-purple-600 hover:bg-purple-100',
                  orange: isActive
                    ? 'bg-orange-600 text-white border-orange-600 shadow-orange-200'
                    : 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-700 hover:border-orange-600 hover:bg-orange-100',
                  red: isActive
                    ? 'bg-red-600 text-white border-red-600 shadow-red-200'
                    : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700 hover:border-red-600 hover:bg-red-100',
                  pink: isActive
                    ? 'bg-pink-600 text-white border-pink-600 shadow-pink-200'
                    : 'bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300 border-pink-200 dark:border-pink-700 hover:border-pink-600 hover:bg-pink-100',
                  indigo: isActive
                    ? 'bg-indigo-600 text-white border-indigo-600 shadow-indigo-200'
                    : 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-700 hover:border-indigo-600 hover:bg-indigo-100',
                  teal: isActive
                    ? 'bg-teal-600 text-white border-teal-600 shadow-teal-200'
                    : 'bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 border-teal-200 dark:border-teal-700 hover:border-teal-600 hover:bg-teal-100',
                  emerald: isActive
                    ? 'bg-emerald-600 text-white border-emerald-600 shadow-emerald-200'
                    : 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700 hover:border-emerald-600 hover:bg-emerald-100',
                  cyan: isActive
                    ? 'bg-cyan-600 text-white border-cyan-600 shadow-cyan-200'
                    : 'bg-cyan-50 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300 border-cyan-200 dark:border-cyan-700 hover:border-cyan-600 hover:bg-cyan-100',
                  violet: isActive
                    ? 'bg-violet-600 text-white border-violet-600 shadow-violet-200'
                    : 'bg-violet-50 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300 border-violet-200 dark:border-violet-700 hover:border-violet-600 hover:bg-violet-100',
                  gray: isActive
                    ? 'bg-gray-600 text-white border-gray-600 shadow-gray-200'
                    : 'bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700 hover:border-gray-600 hover:bg-gray-100'
                };
                return colorMap[color as keyof typeof colorMap] || colorMap.neutral;
              };

              const isActive = currentSelectedCategory === category;
              const colorStyles = getColorStyles(versionInfo.color, isActive);

              return (
                <Badge
                  key={category}
                  variant="outline"
                  className={`cursor-pointer transition-all duration-300 px-3 py-1 text-sm font-medium rounded-lg border ${colorStyles} ${
                    isActive ? 'shadow-sm hover:shadow-md' : ''
                  }`}
                  onClick={() => setSelectedCategory(category)}
                  title={`${versionInfo.desc} (${count} 个特性)`}
                >
                  <span className="flex items-center gap-1.5">
                    {versionInfo.name}
                    <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                      isActive
                        ? 'bg-white/20 text-white'
                        : 'bg-black/10 dark:bg-white/10 opacity-75'
                    }`}>
                      {count}
                    </span>
                  </span>
                </Badge>
              );
            })}
          </div>

          {/* 搜索结果提示 */}
          {searchTerm && (
            <div className="text-center">
              <div className="inline-flex items-center gap-3 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm px-6 py-3 rounded-full border border-neutral-200/50 dark:border-neutral-700/50 text-neutral-600 dark:text-neutral-400 shadow-lg">
                <Search className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">
                  在 <span className="text-blue-600 dark:text-blue-400 font-semibold">{getActiveSearchCount()}</span> 个字段中搜索 
                  "<span className="text-purple-600 dark:text-purple-400 font-semibold">{searchTerm}</span>"，
                  找到 <span className="text-green-600 dark:text-green-400 font-semibold">{filteredData.length}</span> 个结果
                </span>
              </div>
            </div>
          )}
        </div>

        {/* 优化的API卡片网格 */}
        <div className="columns-1 sm:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5 gap-4 space-y-4 px-2">
          {filteredData.map((item) => (
            <ApiCard
              key={item.id}
              item={item}
              currentTheme={currentTheme}
              favorites={favorites}
              onCardClick={handleCardClick}
              onToggleFavorite={toggleFavorite}
              onCopyToClipboard={copyToClipboard}
            />
          ))}
        </div>

        {filteredData.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">没有找到匹配的 API</p>
            {searchTerm && getActiveSearchCount() === 0 && (
              <p className="text-gray-400 text-sm mt-2">请在搜索选项中至少选择一个搜索范围</p>
            )}
          </div>
        )}
      </div>

      {/* 优化的详情Drawer */}
      <Drawer
        title={
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-4">
              <Title level={3} style={{ margin: 0, fontSize: '24px', fontWeight: 600 }}>
                {selectedItem?.title}
              </Title>
              {selectedItem?.isNew && (
                <div className="inline-flex items-center gap-1.5 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                  <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                  <span>NEW</span>
                </div>
              )}
            </div>
            {/* 右侧专业信息展示 */}
            <div className="flex items-center gap-4">
              {/* 版本信息 */}
              {selectedItem?.version && (
                <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-1.5 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    v{selectedItem.version}
                  </span>
                </div>
              )}
              
              {/* 难度等级 */}
              {selectedItem?.difficulty && (
                <div className={`flex items-center gap-2 px-3 py-1.5 rounded-lg ${
                  selectedItem.difficulty === 'easy' 
                    ? 'bg-green-50 dark:bg-green-900/20' 
                    : selectedItem.difficulty === 'medium'
                    ? 'bg-amber-50 dark:bg-amber-900/20'
                    : 'bg-red-50 dark:bg-red-900/20'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    selectedItem.difficulty === 'easy' 
                      ? 'bg-green-500' 
                      : selectedItem.difficulty === 'medium'
                      ? 'bg-amber-500'
                      : 'bg-red-500'
                  }`}></div>
                  <span className={`text-sm font-medium ${
                    selectedItem.difficulty === 'easy' 
                      ? 'text-green-700 dark:text-green-300' 
                      : selectedItem.difficulty === 'medium'
                      ? 'text-amber-700 dark:text-amber-300'
                      : 'text-red-700 dark:text-red-300'
                  }`}>
                    {selectedItem.difficulty === 'easy' ? '入门' : selectedItem.difficulty === 'medium' ? '进阶' : '高级'}
                  </span>
                </div>
              )}
              
              {/* 分类标签 */}
              {selectedItem?.category && (
                <div className="flex items-center gap-2 bg-purple-50 dark:bg-purple-900/20 px-3 py-1.5 rounded-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                    {selectedItem.category}
                  </span>
                </div>
              )}

              {/* 新窗口打开按钮 */}
              <AntButton
                type="text"
                size="small"
                icon={<ExternalLink className="w-4 h-4" />}
                onClick={() => {
                  if (selectedItem) {
                    const url = `/full-tab-render/${selectedItem.id}`;
                    window.open(url, '_blank', 'noopener,noreferrer');
                  }
                }}
                title="在新窗口中打开完整页面"
                className="hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-1"
              >
                <span className="text-xs">新窗口</span>
              </AntButton>
            </div>
          </div>
        }
        placement="right"
        width="85%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        bodyStyle={{ padding: 0, background: '#fafafa' }}
        headerStyle={{ 
          borderBottom: '1px solid #f0f0f0', 
          background: '#fff',
          padding: '20px 24px'
        }}
        className="api-drawer"
        destroyOnClose={true} // 关闭时销毁内容，释放内存
      >
        {selectedItem && (
          <div style={{ height: '100%', background: '#fafafa' }}>
            <div style={{ padding: '24px 24px 16px 24px', background: '#fff' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Paragraph style={{ fontSize: '16px', color: '#666', marginBottom: 0, lineHeight: '1.6', flex: 1, marginRight: '16px' }}>
                  {selectedItem.description}
                </Paragraph>

                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '8px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: '#fff',
                  padding: '6px 16px',
                  borderRadius: '20px',
                  fontSize: '13px',
                  fontWeight: 500,
                  boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
                  flexShrink: 0
                }}>
                  <SettingOutlined style={{ fontSize: '14px' }} />
                  <span>
                    {getVisibleTabs(selectedItem).length} 个可用模块
                  </span>
                </div>
              </div>
            </div>
            
            <div className="relative mt-6">
              {/* 现代化Tab导航 - 参考GitHub/CodePen风格 */}
              <div className="border-b border-slate-200 mx-6 mb-6">
                <div className="flex gap-0 -mb-px">
                  {selectedItem && getVisibleTabs(selectedItem).map(tabKey => {
                    const coreTab = TAB_CONFIG.core.find(t => t.key === tabKey);
                    const advancedTab = TAB_CONFIG.advanced.find(t => t.key === tabKey);
                    const tabConfig = coreTab || advancedTab;

                    if (!tabConfig) return null;

                    return (
                      <button
                        key={tabKey}
                        onClick={() => setActiveTab(tabKey)}
                        className={`relative px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 ${
                          activeTab === tabKey
                            ? 'border-blue-500 text-blue-600 bg-blue-50/50'
                            : 'border-transparent text-slate-600 hover:text-slate-900 hover:border-slate-300'
                        }`}
                        title={tabConfig.description}
                      >
                        <div className="flex items-center gap-2">
                          <span className={`text-sm ${activeTab === tabKey ? 'text-blue-600' : 'text-slate-500'}`}>
                            {tabConfig.icon}
                          </span>
                          <span className="whitespace-nowrap">{tabConfig.label}</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Tab内容区域 */}
              <div className="px-6 pb-6" style={{
                height: 'calc(100vh - 280px)',
                overflow: 'auto'
              }}>
                {selectedItem && (
                  <ErrorBoundary
                    onError={(error, errorInfo) => {
                      console.group('🚨 Drawer Tab渲染错误');
                      console.error('API名称:', selectedItem.title);
                      console.error('当前Tab:', activeTab);
                      console.error('错误:', error);
                      console.error('错误信息:', errorInfo);
                      console.groupEnd();
                    }}
                    fallback={
                      <div style={{
                        padding: '40px 20px',
                        textAlign: 'center',
                        background: '#fafafa',
                        borderRadius: '8px',
                        border: '1px solid #f0f0f0'
                      }}>
                        <Alert
                          message="Tab内容渲染错误"
                          description={`${selectedItem.title} 的 ${activeTab} Tab 出现渲染问题，请尝试切换到其他Tab或刷新页面。`}
                          type="error"
                          showIcon
                          style={{ marginBottom: '16px' }}
                        />
                        <AntButton
                          type="primary"
                          onClick={() => window.location.reload()}
                          style={{ marginRight: '8px' }}
                        >
                          刷新页面
                        </AntButton>
                        <AntButton
                          onClick={() => setActiveTab('basic')}
                        >
                          返回基本信息
                        </AntButton>
                      </div>
                    }
                  >
                    <LazyTabContent
                      tabKey={activeTab}
                      item={selectedItem}
                      copyToClipboard={copyToClipboard}
                      secondaryTabStates={secondaryTabStates}
                      setSecondaryTabStates={setSecondaryTabStates}
                    />
                  </ErrorBoundary>
                )}
              </div>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default CheatSheet; 